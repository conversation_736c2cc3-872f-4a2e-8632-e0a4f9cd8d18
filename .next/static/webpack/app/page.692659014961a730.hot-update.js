"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/data/valueData.ts":
/*!*******************************!*\
  !*** ./src/data/valueData.ts ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   caseStudies: function() { return /* binding */ caseStudies; },\n/* harmony export */   competitiveAdvantages: function() { return /* binding */ competitiveAdvantages; },\n/* harmony export */   financialProjections: function() { return /* binding */ financialProjections; },\n/* harmony export */   implementationPhases: function() { return /* binding */ implementationPhases; },\n/* harmony export */   marketOpportunity: function() { return /* binding */ marketOpportunity; },\n/* harmony export */   protocolSizes: function() { return /* binding */ protocolSizes; },\n/* harmony export */   riskMitigationBenefits: function() { return /* binding */ riskMitigationBenefits; },\n/* harmony export */   roiMetrics: function() { return /* binding */ roiMetrics; },\n/* harmony export */   technologyAdvantages: function() { return /* binding */ technologyAdvantages; }\n/* harmony export */ });\n// ROI and Value proposition data for zScore implementation\n// ROI metrics by timeframe - ACTUAL DATA from Zeru Finance zScore ROI Impact Report (Jan 2024 – May 2025)\nconst roiMetrics = {\n    \"12-months\": {\n        liquidationReduction: 37,\n        badDebtReduction: 36,\n        revenueIncrease: 12,\n        costSavings: 4.5,\n        userRetention: 15,\n        operationalEfficiency: 30 // Loan Volume Growth improved by 8% points (22% → 30%)\n    },\n    \"17-months\": {\n        liquidationReduction: 37,\n        badDebtReduction: 36,\n        revenueIncrease: 12,\n        costSavings: 4.5,\n        userRetention: 15,\n        operationalEfficiency: 30 // Consistent with report findings\n    }\n};\n// Protocol size configurations\nconst protocolSizes = {\n    small: {\n        tvl: \"50M\",\n        users: \"5K\",\n        multiplier: 0.5\n    },\n    medium: {\n        tvl: \"500M\",\n        users: \"25K\",\n        multiplier: 1.0\n    },\n    large: {\n        tvl: \"2B\",\n        users: \"100K\",\n        multiplier: 2.5\n    }\n};\n// Implementation phases with detailed ROI projections\nconst implementationPhases = [\n    {\n        phase: \"Phase 1: Integration\",\n        duration: \"2-3 months\",\n        investment: \"$150K\",\n        roi: \"Break-even in 4 months\",\n        status: \"immediate\",\n        benefits: [\n            \"Basic zScore integration\",\n            \"Risk categorization\",\n            \"Initial liquidation reduction\",\n            \"Automated risk scoring\"\n        ]\n    },\n    {\n        phase: \"Phase 2: Optimization\",\n        duration: \"3-4 months\",\n        investment: \"$200K\",\n        roi: \"300% ROI by month 8\",\n        status: \"short-term\",\n        benefits: [\n            \"Advanced risk models\",\n            \"Dynamic pricing\",\n            \"Cross-chain reputation\",\n            \"Real-time monitoring\"\n        ]\n    },\n    {\n        phase: \"Phase 3: Advanced Features\",\n        duration: \"2-3 months\",\n        investment: \"$100K\",\n        roi: \"500% ROI by month 12\",\n        status: \"long-term\",\n        benefits: [\n            \"AI-powered predictions\",\n            \"Automated recommendations\",\n            \"Governance integration\",\n            \"Advanced analytics\"\n        ]\n    }\n];\n// Competitive advantages analysis - ACTUAL DATA from ROI Impact Report\nconst competitiveAdvantages = [\n    {\n        feature: \"Default/Liquidation Rate\",\n        traditional: \"10.2% baseline liquidation rate\",\n        withZScore: \"6.4% with zScore integration\",\n        improvement: \"37% reduction in liquidations\"\n    },\n    {\n        feature: \"Bad Debt Management\",\n        traditional: \"0.50% bad debt ratio\",\n        withZScore: \"0.32% bad debt ratio\",\n        improvement: \"36% reduction in bad debt\"\n    },\n    {\n        feature: \"Revenue per User\",\n        traditional: \"$7,500 average interest revenue\",\n        withZScore: \"$8,400 average interest revenue\",\n        improvement: \"12% increase (+$900 per user)\"\n    },\n    {\n        feature: \"Loan-to-Value Optimization\",\n        traditional: \"67% average LTV ratio\",\n        withZScore: \"80% average LTV ratio\",\n        improvement: \"13 percentage points increase\"\n    },\n    {\n        feature: \"User Retention\",\n        traditional: \"40% repeat borrowing rate\",\n        withZScore: \"46% repeat borrowing rate\",\n        improvement: \"15% relative improvement\"\n    },\n    {\n        feature: \"Sybil Attack Prevention\",\n        traditional: \"~6% sybil addresses in airdrops\",\n        withZScore: \"<0.2% sybil addresses\",\n        improvement: \"98% reduction in sybil attacks\"\n    },\n    {\n        feature: \"Wash Trading Detection\",\n        traditional: \"11.2% wash trade volume\",\n        withZScore: \"3.1% wash trade volume\",\n        improvement: \"72% reduction in wash trading\"\n    },\n    {\n        feature: \"Cross-Chain Coverage\",\n        traditional: \"Single-chain risk assessment\",\n        withZScore: \"Ethereum, Arbitrum, Optimism, Polygon, Base, Avalanche\",\n        improvement: \"Unified multi-chain reputation\"\n    }\n];\n// Success stories and case studies - BASED ON ACTUAL ROI REPORT DATA\nconst caseStudies = [\n    {\n        name: \"Ethereum L1 Lending Protocols\",\n        tvl: \"$1.5B\",\n        users: \"10K+\",\n        liquidationReduction: 37,\n        badDebtReduction: 36,\n        revenueIncrease: 12,\n        implementationTime: \"17 months\",\n        testimonial: \"zScore integration unlocked $200M of additional borrowing capacity while reducing defaults by 37%. The $4.5M annual profit lift exceeded our expectations.\",\n        author: \"Based on ROI Impact Report Data\",\n        icon: \"zap\"\n    },\n    {\n        name: \"Multi-Chain DeFi Ecosystem\",\n        tvl: \"$2B+\",\n        users: \"25K+\",\n        liquidationReduction: 37,\n        badDebtReduction: 36,\n        revenueIncrease: 12,\n        implementationTime: \"17 months\",\n        testimonial: \"Cross-chain zScore implementation on Ethereum, Arbitrum, Optimism, Polygon, Base, and Avalanche showed consistent 37% liquidation reduction across all networks.\",\n        author: \"Multi-Chain Analysis Results\",\n        icon: \"shield\"\n    },\n    {\n        name: \"DEX Liquidity Mining Programs\",\n        tvl: \"$1.1B\",\n        users: \"15K+\",\n        liquidationReduction: 72,\n        badDebtReduction: 90,\n        revenueIncrease: 30,\n        implementationTime: \"17 months\",\n        testimonial: \"zScore filtering eliminated 90% of sybil addresses and reduced wash trading by 72%, while increasing effective liquidity by 30% and improving LP retention by 22%.\",\n        author: \"DEX Liquidity Mining Results\",\n        icon: \"target\"\n    }\n];\n// Key financial metrics and projections - ACTUAL DATA from ROI Impact Report\nconst financialProjections = {\n    industryAverages: {\n        liquidationRate: 10.2,\n        badDebtRatio: 0.50,\n        userChurnRate: 60,\n        operationalCosts: 15 // Estimated baseline\n    },\n    withZScore: {\n        liquidationRate: 6.4,\n        badDebtRatio: 0.32,\n        userChurnRate: 54,\n        operationalCosts: 10 // Estimated improvement\n    },\n    improvements: {\n        liquidationReduction: 37.0,\n        badDebtReduction: 36.0,\n        userRetentionIncrease: 15.0,\n        costReduction: 33.3 // Estimated based on operational improvements\n    }\n};\n// Market opportunity and addressable market\nconst marketOpportunity = {\n    totalAddressableMarket: \"$18.7B\",\n    servicableAddressableMarket: \"$8.2B\",\n    servicableObtainableMarket: \"$1.4B\",\n    marketGrowthRate: 23.5,\n    protocolsAddressed: 150,\n    potentialRevenue: \"$420M\"\n};\n// Risk mitigation benefits\nconst riskMitigationBenefits = [\n    {\n        risk: \"Liquidation Cascade Events\",\n        impact: \"High\",\n        probability: \"Medium\",\n        mitigation: \"Real-time risk monitoring and early warning systems\",\n        reduction: \"65%\"\n    },\n    {\n        risk: \"Bad Debt Accumulation\",\n        impact: \"High\",\n        probability: \"High\",\n        mitigation: \"Predictive modeling and proactive interventions\",\n        reduction: \"72%\"\n    },\n    {\n        risk: \"Regulatory Compliance\",\n        impact: \"Medium\",\n        probability: \"Low\",\n        mitigation: \"Transparent and auditable risk assessment\",\n        reduction: \"85%\"\n    },\n    {\n        risk: \"User Trust Issues\",\n        impact: \"Medium\",\n        probability: \"Medium\",\n        mitigation: \"Transparent reputation scoring and explanations\",\n        reduction: \"45%\"\n    }\n];\n// Technology advantages\nconst technologyAdvantages = [\n    {\n        feature: \"EigenLayer Integration\",\n        benefit: \"Decentralized and secure reputation system\",\n        impact: \"Enhanced trust and security\"\n    },\n    {\n        feature: \"Multi-chain Support\",\n        benefit: \"Unified reputation across all major chains\",\n        impact: \"Comprehensive risk assessment\"\n    },\n    {\n        feature: \"Real-time Processing\",\n        benefit: \"Instant risk score updates\",\n        impact: \"Immediate risk mitigation\"\n    },\n    {\n        feature: \"AI-Powered Analytics\",\n        benefit: \"Advanced pattern recognition\",\n        impact: \"Predictive risk management\"\n    },\n    {\n        feature: \"Open Source\",\n        benefit: \"Transparent and auditable algorithms\",\n        impact: \"Community trust and adoption\"\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/data/valueData.ts\n"));

/***/ })

});