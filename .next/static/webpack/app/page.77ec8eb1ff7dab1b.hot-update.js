"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/data/valueData.ts":
/*!*******************************!*\
  !*** ./src/data/valueData.ts ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   caseStudies: function() { return /* binding */ caseStudies; },\n/* harmony export */   competitiveAdvantages: function() { return /* binding */ competitiveAdvantages; },\n/* harmony export */   financialProjections: function() { return /* binding */ financialProjections; },\n/* harmony export */   implementationPhases: function() { return /* binding */ implementationPhases; },\n/* harmony export */   marketOpportunity: function() { return /* binding */ marketOpportunity; },\n/* harmony export */   protocolSizes: function() { return /* binding */ protocolSizes; },\n/* harmony export */   riskMitigationBenefits: function() { return /* binding */ riskMitigationBenefits; },\n/* harmony export */   roiMetrics: function() { return /* binding */ roiMetrics; },\n/* harmony export */   technologyAdvantages: function() { return /* binding */ technologyAdvantages; }\n/* harmony export */ });\n// ROI and Value proposition data for zScore implementation\n// ROI metrics by timeframe - ACTUAL DATA from Zeru Finance zScore ROI Impact Report (Jan 2024 – May 2025)\nconst roiMetrics = {\n    \"12-months\": {\n        liquidationReduction: 37,\n        badDebtReduction: 36,\n        revenueIncrease: 12,\n        costSavings: 4.5,\n        userRetention: 15,\n        operationalEfficiency: 30 // Loan Volume Growth improved by 8% points (22% → 30%)\n    },\n    \"17-months\": {\n        liquidationReduction: 37,\n        badDebtReduction: 36,\n        revenueIncrease: 12,\n        costSavings: 4.5,\n        userRetention: 15,\n        operationalEfficiency: 30 // Consistent with report findings\n    }\n};\n// Protocol size configurations\nconst protocolSizes = {\n    small: {\n        tvl: \"50M\",\n        users: \"5K\",\n        multiplier: 0.5\n    },\n    medium: {\n        tvl: \"500M\",\n        users: \"25K\",\n        multiplier: 1.0\n    },\n    large: {\n        tvl: \"2B\",\n        users: \"100K\",\n        multiplier: 2.5\n    }\n};\n// Implementation phases with detailed ROI projections\nconst implementationPhases = [\n    {\n        phase: \"Phase 1: Integration\",\n        duration: \"2-3 months\",\n        investment: \"$150K\",\n        roi: \"Break-even in 4 months\",\n        status: \"immediate\",\n        benefits: [\n            \"Basic zScore integration\",\n            \"Risk categorization\",\n            \"Initial liquidation reduction\",\n            \"Automated risk scoring\"\n        ]\n    },\n    {\n        phase: \"Phase 2: Optimization\",\n        duration: \"3-4 months\",\n        investment: \"$200K\",\n        roi: \"300% ROI by month 8\",\n        status: \"short-term\",\n        benefits: [\n            \"Advanced risk models\",\n            \"Dynamic pricing\",\n            \"Cross-chain reputation\",\n            \"Real-time monitoring\"\n        ]\n    },\n    {\n        phase: \"Phase 3: Advanced Features\",\n        duration: \"2-3 months\",\n        investment: \"$100K\",\n        roi: \"500% ROI by month 12\",\n        status: \"long-term\",\n        benefits: [\n            \"AI-powered predictions\",\n            \"Automated recommendations\",\n            \"Governance integration\",\n            \"Advanced analytics\"\n        ]\n    }\n];\n// Competitive advantages analysis\nconst competitiveAdvantages = [\n    {\n        feature: \"Real-time Risk Assessment\",\n        traditional: \"Manual review, 24-48h delay\",\n        withZScore: \"Instant automated assessment\",\n        improvement: \"95% faster processing\"\n    },\n    {\n        feature: \"Liquidation Prediction\",\n        traditional: \"Reactive, post-event analysis\",\n        withZScore: \"Proactive, 24-48h advance warning\",\n        improvement: \"42% reduction in liquidations\"\n    },\n    {\n        feature: \"Cross-chain Reputation\",\n        traditional: \"Siloed, single-chain data\",\n        withZScore: \"Unified, multi-chain reputation\",\n        improvement: \"65% better risk assessment\"\n    },\n    {\n        feature: \"User Experience\",\n        traditional: \"Complex, manual processes\",\n        withZScore: \"Seamless, automated onboarding\",\n        improvement: \"35% higher user retention\"\n    },\n    {\n        feature: \"Bad Debt Management\",\n        traditional: \"Reactive liquidations\",\n        withZScore: \"Predictive risk mitigation\",\n        improvement: \"65% reduction in bad debt\"\n    },\n    {\n        feature: \"Operational Costs\",\n        traditional: \"High manual oversight\",\n        withZScore: \"Automated risk management\",\n        improvement: \"55% cost reduction\"\n    }\n];\n// Success stories and case studies\nconst caseStudies = [\n    {\n        name: \"DeFi Protocol Alpha\",\n        tvl: \"$1.2B\",\n        users: \"50K+\",\n        liquidationReduction: 45,\n        badDebtReduction: 62,\n        revenueIncrease: 32,\n        implementationTime: \"8 months\",\n        testimonial: \"zScore implementation reduced our liquidation events by 45% and significantly improved our risk assessment capabilities. The ROI was evident within 6 months.\",\n        author: \"CTO, DeFi Protocol Alpha\",\n        icon: \"zap\"\n    },\n    {\n        name: \"Lending Platform Beta\",\n        tvl: \"$800M\",\n        users: \"35K+\",\n        liquidationReduction: 38,\n        badDebtReduction: 55,\n        revenueIncrease: 28,\n        implementationTime: \"10 months\",\n        testimonial: \"The automated risk assessment and cross-chain reputation tracking transformed our lending operations. User experience improved dramatically.\",\n        author: \"Head of Risk, Lending Platform Beta\",\n        icon: \"shield\"\n    },\n    {\n        name: \"Multi-Chain Lender Gamma\",\n        tvl: \"$2.1B\",\n        users: \"75K+\",\n        liquidationReduction: 52,\n        badDebtReduction: 71,\n        revenueIncrease: 41,\n        implementationTime: \"6 months\",\n        testimonial: \"Cross-chain reputation tracking was a game-changer. We saw immediate improvements in risk assessment accuracy and user trust.\",\n        author: \"CEO, Multi-Chain Lender Gamma\",\n        icon: \"target\"\n    }\n];\n// Key financial metrics and projections - ACTUAL DATA from ROI Impact Report\nconst financialProjections = {\n    industryAverages: {\n        liquidationRate: 10.2,\n        badDebtRatio: 0.50,\n        userChurnRate: 60,\n        operationalCosts: 15 // Estimated baseline\n    },\n    withZScore: {\n        liquidationRate: 6.4,\n        badDebtRatio: 0.32,\n        userChurnRate: 54,\n        operationalCosts: 10 // Estimated improvement\n    },\n    improvements: {\n        liquidationReduction: 37.0,\n        badDebtReduction: 36.0,\n        userRetentionIncrease: 15.0,\n        costReduction: 33.3 // Estimated based on operational improvements\n    }\n};\n// Market opportunity and addressable market\nconst marketOpportunity = {\n    totalAddressableMarket: \"$18.7B\",\n    servicableAddressableMarket: \"$8.2B\",\n    servicableObtainableMarket: \"$1.4B\",\n    marketGrowthRate: 23.5,\n    protocolsAddressed: 150,\n    potentialRevenue: \"$420M\"\n};\n// Risk mitigation benefits\nconst riskMitigationBenefits = [\n    {\n        risk: \"Liquidation Cascade Events\",\n        impact: \"High\",\n        probability: \"Medium\",\n        mitigation: \"Real-time risk monitoring and early warning systems\",\n        reduction: \"65%\"\n    },\n    {\n        risk: \"Bad Debt Accumulation\",\n        impact: \"High\",\n        probability: \"High\",\n        mitigation: \"Predictive modeling and proactive interventions\",\n        reduction: \"72%\"\n    },\n    {\n        risk: \"Regulatory Compliance\",\n        impact: \"Medium\",\n        probability: \"Low\",\n        mitigation: \"Transparent and auditable risk assessment\",\n        reduction: \"85%\"\n    },\n    {\n        risk: \"User Trust Issues\",\n        impact: \"Medium\",\n        probability: \"Medium\",\n        mitigation: \"Transparent reputation scoring and explanations\",\n        reduction: \"45%\"\n    }\n];\n// Technology advantages\nconst technologyAdvantages = [\n    {\n        feature: \"EigenLayer Integration\",\n        benefit: \"Decentralized and secure reputation system\",\n        impact: \"Enhanced trust and security\"\n    },\n    {\n        feature: \"Multi-chain Support\",\n        benefit: \"Unified reputation across all major chains\",\n        impact: \"Comprehensive risk assessment\"\n    },\n    {\n        feature: \"Real-time Processing\",\n        benefit: \"Instant risk score updates\",\n        impact: \"Immediate risk mitigation\"\n    },\n    {\n        feature: \"AI-Powered Analytics\",\n        benefit: \"Advanced pattern recognition\",\n        impact: \"Predictive risk management\"\n    },\n    {\n        feature: \"Open Source\",\n        benefit: \"Transparent and auditable algorithms\",\n        impact: \"Community trust and adoption\"\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/data/valueData.ts\n"));

/***/ })

});