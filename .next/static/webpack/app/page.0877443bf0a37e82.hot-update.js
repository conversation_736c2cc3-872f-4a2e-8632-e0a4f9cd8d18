"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/TabSystem.tsx":
/*!**************************************!*\
  !*** ./src/components/TabSystem.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TabSystem; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _lending_RealUsecase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lending/RealUsecase */ \"(app-pages-browser)/./src/components/lending/RealUsecase.tsx\");\n/* harmony import */ var _lending_Global__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lending/Global */ \"(app-pages-browser)/./src/components/lending/Global.tsx\");\n/* harmony import */ var _lending_Ideal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lending/Ideal */ \"(app-pages-browser)/./src/components/lending/Ideal.tsx\");\n/* harmony import */ var _lending_Value__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./lending/Value */ \"(app-pages-browser)/./src/components/lending/Value.tsx\");\n/* harmony import */ var _airdrops_Value__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./airdrops/Value */ \"(app-pages-browser)/./src/components/airdrops/Value.tsx\");\n/* harmony import */ var _ComingSoon__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ComingSoon */ \"(app-pages-browser)/./src/components/ComingSoon.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nconst tabs = [\n    {\n        id: \"real-usecase\",\n        name: \"Real-usecase\",\n        description: \"Live data analysis\"\n    },\n    {\n        id: \"global\",\n        name: \"Global\",\n        description: \"Worldwide metrics\"\n    },\n    {\n        id: \"ideal\",\n        name: \"Ideal usecases\",\n        description: \"Best practices\"\n    },\n    {\n        id: \"value\",\n        name: \"Value\",\n        description: \"ROI & Business Impact\"\n    }\n];\nfunction TabSystem(param) {\n    let { selectedSector, selectedTab, onTabChange } = param;\n    const renderContent = ()=>{\n        if (selectedSector === \"lending\") {\n            switch(selectedTab){\n                case \"real-usecase\":\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lending_RealUsecase__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 18\n                    }, this);\n                case \"global\":\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lending_Global__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 18\n                    }, this);\n                case \"ideal\":\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lending_Ideal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 18\n                    }, this);\n                case \"value\":\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lending_Value__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 18\n                    }, this);\n                default:\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lending_RealUsecase__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 18\n                    }, this);\n            }\n        }\n        if (selectedSector === \"airdrops\") {\n            switch(selectedTab){\n                case \"value\":\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_airdrops_Value__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 18\n                    }, this);\n                case \"real-usecase\":\n                case \"global\":\n                case \"ideal\":\n                default:\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ComingSoon__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        sector: selectedSector\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 18\n                    }, this);\n            }\n        }\n        // For other sectors, show coming soon\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ComingSoon__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            sector: selectedSector\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n            lineNumber: 54,\n            columnNumber: 12\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface border-b border-border-color px-4 sm:px-6 py-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg sm:text-xl font-semibold text-text-primary capitalize\",\n                                    children: [\n                                        selectedSector,\n                                        \" Analytics\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-text-secondary hidden sm:block\",\n                                    children: [\n                                        \"Comprehensive analysis and insights for \",\n                                        selectedSector,\n                                        \" use cases\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-1 bg-surface-light rounded-lg p-1 overflow-x-auto\",\n                        children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>onTabChange(tab.id),\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"flex-1 min-w-0 px-2 sm:px-4 py-3 rounded-md text-xs sm:text-sm font-medium transition-all duration-200\", \"hover:bg-surface hover:text-text-primary whitespace-nowrap\", selectedTab === tab.id ? \"bg-primary text-white shadow-sm\" : \"text-text-secondary\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium\",\n                                            children: tab.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs opacity-75 mt-1 hidden sm:block\",\n                                            children: tab.description\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 15\n                                }, this)\n                            }, tab.id, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-auto\",\n                children: renderContent()\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, this);\n}\n_c = TabSystem;\nvar _c;\n$RefreshReg$(_c, \"TabSystem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TabSystem.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/airdrops/Value.tsx":
/*!*******************************************!*\
  !*** ./src/components/airdrops/Value.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AirdropValue; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calculator_CheckCircle_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calculator,CheckCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calculator_CheckCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calculator,CheckCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calculator.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calculator_CheckCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calculator,CheckCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction AirdropValue() {\n    _s();\n    const [airdropBudget, setAirdropBudget] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    const [expectedRecipients, setExpectedRecipients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(100000);\n    const calculateSavings = ()=>{\n        const sybilWaste = airdropBudget * 0.06 * 0.95;\n        const realUserCost = airdropBudget / (expectedRecipients * 0.94);\n        const improvedCost = realUserCost * 0.4;\n        return {\n            sybilWaste: sybilWaste.toFixed(1),\n            realUserCost: realUserCost.toFixed(0),\n            improvedCost: improvedCost.toFixed(0),\n            totalSavings: sybilWaste.toFixed(1)\n        };\n    };\n    const savings = calculateSavings();\n    const funnelData = {\n        baseline: {\n            claim: 95,\n            hold1Week: 20,\n            engage: 5,\n            active3Month: 3.5\n        },\n        withZScore: {\n            claim: 98,\n            hold1Week: 45,\n            engage: 15,\n            active3Month: 10.2\n        }\n    };\n    const realWorldExamples = [\n        {\n            name: \"Starknet Airdrop Attack\",\n            date: \"Feb 2024\",\n            impact: \"$5.4M stolen by ~3,161 fake wallets\",\n            priceImpact: \"57% price drop\",\n            prevention: \"zScore would have prevented 95% of this attack\",\n            color: \"red\"\n        },\n        {\n            name: \"LayerZero Analysis\",\n            date: \"2024\",\n            impact: \"~341k Sybil wallets (5.9% of recipients)\",\n            priceImpact: \"Significant token dumping\",\n            prevention: \"zScore reduces to <0.2% Sybil participation\",\n            color: \"blue\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-4 sm:p-6 space-y-4 sm:space-y-6 bg-background min-h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface border border-border-color rounded-xl p-4 sm:p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-xl sm:text-2xl font-bold text-text-primary mb-2\",\n                        children: \"Airdrop & Sybil Resistance ROI Impact\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-text-secondary mb-4 text-sm sm:text-base\",\n                        children: \"Proven results from LayerZero and Starknet analysis showing how zScore eliminates 98% of Sybil attacks while improving user engagement by 3\\xd7.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"px-3 py-1 bg-primary/10 text-primary rounded-full text-xs font-medium\",\n                                children: \"LayerZero Analysis\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"px-3 py-1 bg-secondary/10 text-secondary rounded-full text-xs font-medium\",\n                                children: \"Starknet Case Study\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"px-3 py-1 bg-accent/10 text-accent rounded-full text-xs font-medium\",\n                                children: \"98% Sybil Reduction\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-green-50 to-blue-50 border-2 border-green-200 rounded-xl p-4 sm:p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calculator_CheckCircle_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"w-8 h-8 text-green-600 mr-3\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-bold text-green-800\",\n                                children: \"ACTUAL PROVEN RESULTS\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-green-700 font-medium mb-2\",\n                                children: [\n                                    \"All metrics below are based on \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"real analysis\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 44\n                                    }, this),\n                                    \" of LayerZero and Starknet airdrop data\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-green-600 text-sm\",\n                                children: [\n                                    \"Analysis Period: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"January 2024 - May 2025\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 30\n                                    }, this),\n                                    \" | Methodology: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Modeled on actual exploit data\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 26\n                                    }, this),\n                                    \" | Statistical Significance: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"95%+ confidence where tested\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 39\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/70 rounded-lg p-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-600\",\n                                        children: \"98%\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-green-700\",\n                                        children: \"Sybil Reduction\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-green-600\",\n                                        children: \"6% → <0.2%\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/70 rounded-lg p-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-600\",\n                                        children: \"3\\xd7\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-green-700\",\n                                        children: \"User Engagement\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-green-600\",\n                                        children: \"5% → 15%\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/70 rounded-lg p-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-600\",\n                                        children: \"60%\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-green-700\",\n                                        children: \"Cost Reduction\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-green-600\",\n                                        children: \"$200 → $80 per user\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/70 rounded-lg p-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-600\",\n                                        children: \"2.25\\xd7\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-green-700\",\n                                        children: \"Holder Retention\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-green-600\",\n                                        children: \"20% → 45%\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface border border-border-color rounded-xl p-4 sm:p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-text-primary mb-4 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calculator_CheckCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-5 h-5 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, this),\n                            \"Airdrop ROI Calculator\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-text-primary mb-2\",\n                                        children: \"Airdrop Budget ($ millions)\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"number\",\n                                        value: airdropBudget,\n                                        onChange: (e)=>setAirdropBudget(Number(e.target.value)),\n                                        className: \"w-full p-3 border border-border-color rounded-lg bg-surface-light focus:ring-2 focus:ring-primary focus:border-transparent\",\n                                        min: \"1\",\n                                        max: \"100\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-text-primary mb-2\",\n                                        children: \"Expected Recipients\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"number\",\n                                        value: expectedRecipients,\n                                        onChange: (e)=>setExpectedRecipients(Number(e.target.value)),\n                                        className: \"w-full p-3 border border-border-color rounded-lg bg-surface-light focus:ring-2 focus:ring-primary focus:border-transparent\",\n                                        min: \"1000\",\n                                        max: \"1000000\",\n                                        step: \"1000\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-red-800 mb-2\",\n                                        children: \"Sybil Waste Prevented\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-red-600 mb-1\",\n                                        children: [\n                                            \"$\",\n                                            savings.sybilWaste,\n                                            \"M\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-red-700\",\n                                        children: \"95% of Sybil tokens saved\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-blue-800 mb-2\",\n                                        children: \"Cost per Real User\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-bold text-blue-600 mb-1\",\n                                        children: [\n                                            \"$\",\n                                            savings.realUserCost,\n                                            \" → $\",\n                                            savings.improvedCost\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-blue-700\",\n                                        children: \"60% cost reduction\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 border border-green-200 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-green-800 mb-2\",\n                                        children: \"Total Savings\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-600 mb-1\",\n                                        children: [\n                                            \"$\",\n                                            savings.totalSavings,\n                                            \"M\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-green-700\",\n                                        children: \"Tokens retained/reallocated\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-purple-50 border border-purple-200 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-purple-800 mb-2\",\n                                        children: \"Engagement Boost\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-purple-600 mb-1\",\n                                        children: \"3\\xd7\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-purple-700\",\n                                        children: \"Higher protocol usage\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface border border-border-color rounded-xl p-4 sm:p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-text-primary mb-6 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calculator_CheckCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-5 h-5 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 11\n                            }, this),\n                            \"Real-World Sybil Attacks & Prevention\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                        children: realWorldExamples.map((example, index)=>{\n                            const bgClass = example.color === \"red\" ? \"bg-red-50\" : \"bg-blue-50\";\n                            const borderClass = example.color === \"red\" ? \"border-red-200\" : \"border-blue-200\";\n                            const textClass = example.color === \"red\" ? \"text-red-800\" : \"text-blue-800\";\n                            const badgeBgClass = example.color === \"red\" ? \"bg-red-100\" : \"bg-blue-100\";\n                            const badgeTextClass = example.color === \"red\" ? \"text-red-700\" : \"text-blue-700\";\n                            const contentTextClass = example.color === \"red\" ? \"text-red-700\" : \"text-blue-700\";\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat(bgClass, \" border \").concat(borderClass, \" rounded-lg p-4\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold \".concat(textClass),\n                                                children: example.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-2 py-1 \".concat(badgeBgClass, \" \").concat(badgeTextClass, \" rounded-full text-xs font-medium\"),\n                                                children: example.date\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm \".concat(contentTextClass),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Attack Impact:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \" \",\n                                                    example.impact\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm \".concat(contentTextClass),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Price Impact:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \" \",\n                                                    example.priceImpact\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-green-50 border border-green-200 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium text-green-800 mb-1\",\n                                                children: \"zScore Prevention:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-green-700\",\n                                                children: example.prevention\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/airdrops/Value.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, this);\n}\n_s(AirdropValue, \"vVpEl/Ch/rpYTZdQCa8wHIROLKE=\");\n_c = AirdropValue;\nvar _c;\n$RefreshReg$(_c, \"AirdropValue\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/airdrops/Value.tsx\n"));

/***/ })

});