"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/lending/Value.tsx":
/*!******************************************!*\
  !*** ./src/components/lending/Value.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LendingValue; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,Award,BarChart3,Calculator,CheckCircle,Clock,DollarSign,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,Award,BarChart3,Calculator,CheckCircle,Clock,DollarSign,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,Award,BarChart3,Calculator,CheckCircle,Clock,DollarSign,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,Award,BarChart3,Calculator,CheckCircle,Clock,DollarSign,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,Award,BarChart3,Calculator,CheckCircle,Clock,DollarSign,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calculator.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,Award,BarChart3,Calculator,CheckCircle,Clock,DollarSign,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,Award,BarChart3,Calculator,CheckCircle,Clock,DollarSign,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,Award,BarChart3,Calculator,CheckCircle,Clock,DollarSign,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,Award,BarChart3,Calculator,CheckCircle,Clock,DollarSign,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,Award,BarChart3,Calculator,CheckCircle,Clock,DollarSign,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,Award,BarChart3,Calculator,CheckCircle,Clock,DollarSign,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,Award,BarChart3,Calculator,CheckCircle,Clock,DollarSign,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _data_valueData__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/valueData */ \"(app-pages-browser)/./src/data/valueData.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction LendingValue() {\n    _s();\n    const [selectedTimeframe, setSelectedTimeframe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"12-months\");\n    const [protocolSize, setProtocolSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"medium\");\n    const currentMetrics = _data_valueData__WEBPACK_IMPORTED_MODULE_2__.roiMetrics[selectedTimeframe];\n    const currentSize = _data_valueData__WEBPACK_IMPORTED_MODULE_2__.protocolSizes[protocolSize];\n    // Calculate ROI based on protocol size\n    const calculateROI = (baseValue)=>{\n        return (baseValue * currentSize.multiplier).toFixed(1);\n    };\n    const businessImpacts = [\n        {\n            title: \"Liquidation Risk Reduction\",\n            value: \"\".concat(currentMetrics.liquidationReduction, \"%\"),\n            description: \"Decrease in unexpected liquidations\",\n            icon: _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            color: \"text-green-500\",\n            bgColor: \"bg-green-50\",\n            trend: \"+15% vs industry average\"\n        },\n        {\n            title: \"Bad Debt Mitigation\",\n            value: \"\".concat(currentMetrics.badDebtReduction, \"%\"),\n            description: \"Reduction in protocol bad debt\",\n            icon: _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            color: \"text-blue-500\",\n            bgColor: \"bg-blue-50\",\n            trend: \"Industry leading performance\"\n        },\n        {\n            title: \"Revenue Growth\",\n            value: \"\".concat(currentMetrics.revenueIncrease, \"%\"),\n            description: \"Increase in protocol revenue\",\n            icon: _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            color: \"text-purple-500\",\n            bgColor: \"bg-purple-50\",\n            trend: \"$\".concat(calculateROI(currentMetrics.costSavings), \"M additional revenue\")\n        },\n        {\n            title: \"User Retention\",\n            value: \"\".concat(currentMetrics.userRetention, \"%\"),\n            description: \"Improvement in user retention\",\n            icon: _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            color: \"text-orange-500\",\n            bgColor: \"bg-orange-50\",\n            trend: \"Higher lifetime value\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-4 sm:p-6 space-y-4 sm:space-y-6 bg-background min-h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface border border-border-color rounded-xl p-4 sm:p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col lg:flex-row lg:items-start lg:justify-between space-y-4 lg:space-y-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl sm:text-2xl font-bold text-text-primary mb-2\",\n                                    children: \"zScore ROI Impact Report\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-text-secondary mb-4 text-sm sm:text-base\",\n                                    children: \"Comprehensive analysis of financial benefits and business impact from implementing Zeru Finance zScore reputation system in DeFi lending protocols.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-3 py-1 bg-primary/10 text-primary rounded-full text-xs font-medium\",\n                                            children: \"Jan 2024 - May 2025\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-3 py-1 bg-secondary/10 text-secondary rounded-full text-xs font-medium\",\n                                            children: \"Live Data Analysis\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-3 py-1 bg-accent/10 text-accent rounded-full text-xs font-medium\",\n                                            children: \"Proven ROI\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-primary/10 to-secondary/10 border border-primary/20 rounded-lg p-4 lg:ml-4 flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-primary mb-1\",\n                                        children: \"500%+\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-text-secondary\",\n                                        children: \"Average ROI\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-text-muted mt-1\",\n                                        children: \"Within 12 months\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface border border-border-color rounded-xl p-4 sm:p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-text-primary mb-4 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-5 h-5 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, this),\n                            \"ROI Calculator\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-text-primary mb-2\",\n                                        children: \"Timeframe\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: selectedTimeframe,\n                                        onChange: (e)=>setSelectedTimeframe(e.target.value),\n                                        className: \"w-full p-3 border border-border-color rounded-lg bg-surface-light focus:ring-2 focus:ring-primary focus:border-transparent\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"12-months\",\n                                                children: \"12 Months\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"24-months\",\n                                                children: \"24 Months\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-text-primary mb-2\",\n                                        children: \"Protocol Size\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: protocolSize,\n                                        onChange: (e)=>setProtocolSize(e.target.value),\n                                        className: \"w-full p-3 border border-border-color rounded-lg bg-surface-light focus:ring-2 focus:ring-primary focus:border-transparent\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"small\",\n                                                children: \"Small ($50M TVL, 5K users)\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"medium\",\n                                                children: \"Medium ($500M TVL, 25K users)\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"large\",\n                                                children: \"Large ($2B+ TVL, 100K+ users)\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6\",\n                children: businessImpacts.map((impact, index)=>{\n                    const Icon = impact.icon;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-surface border border-border-color rounded-xl p-4 sm:p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 \".concat(impact.bgColor, \" rounded-lg flex items-center justify-center\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: \"w-6 h-6 \".concat(impact.color)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-4 h-4 text-secondary\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-semibold text-text-primary mb-2\",\n                                children: impact.title\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-text-primary mb-1\",\n                                children: impact.value\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-text-secondary mb-2\",\n                                children: impact.description\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-secondary font-medium\",\n                                children: impact.trend\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface border border-border-color rounded-xl p-4 sm:p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-text-primary mb-6 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"w-5 h-5 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, this),\n                            \"Implementation Timeline & Expected ROI\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                        children: _data_valueData__WEBPACK_IMPORTED_MODULE_2__.implementationPhases.map((phase, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-surface-light rounded-lg p-4 border border-border-color\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-text-primary\",\n                                                children: phase.phase\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(phase.status === \"immediate\" ? \"bg-green-100 text-green-700\" : phase.status === \"short-term\" ? \"bg-blue-100 text-blue-700\" : \"bg-purple-100 text-purple-700\"),\n                                                children: phase.status\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-text-secondary\",\n                                                        children: \"Duration:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-text-primary\",\n                                                        children: phase.duration\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-text-secondary\",\n                                                        children: \"Investment:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-text-primary\",\n                                                        children: phase.investment\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-text-secondary\",\n                                                        children: \"Expected ROI:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 202,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-secondary\",\n                                                        children: phase.roi\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-medium text-text-primary\",\n                                                children: \"Key Benefits:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, this),\n                                            phase.benefits.map((benefit, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"w-3 h-3 text-secondary flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                            lineNumber: 210,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-text-secondary\",\n                                                            children: benefit\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, idx, true, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                lineNumber: 174,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface border border-border-color rounded-xl p-4 sm:p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-text-primary mb-6 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"w-5 h-5 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 11\n                            }, this),\n                            \"Competitive Advantage Analysis\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        className: \"border-b border-border-color\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 font-semibold text-text-primary\",\n                                                children: \"Feature\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 font-semibold text-text-primary\",\n                                                children: \"Traditional Approach\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 font-semibold text-text-primary\",\n                                                children: \"With zScore\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 font-semibold text-text-primary\",\n                                                children: \"Improvement\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: _data_valueData__WEBPACK_IMPORTED_MODULE_2__.competitiveAdvantages.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"border-b border-border-color/50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-4 px-4 font-medium text-text-primary\",\n                                                    children: item.feature\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-4 px-4 text-text-secondary\",\n                                                    children: item.traditional\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-4 px-4 text-text-secondary\",\n                                                    children: item.withZScore\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-4 px-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 bg-secondary/10 text-secondary rounded-full text-sm font-medium\",\n                                                        children: item.improvement\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                lineNumber: 221,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-primary/5 to-secondary/5 border border-primary/20 rounded-xl p-4 sm:p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-text-primary mb-6 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"w-5 h-5 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 11\n                            }, this),\n                            \"Financial Impact Summary (\",\n                            selectedTimeframe,\n                            \")\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/50 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-text-primary mb-2\",\n                                        children: \"Cost Savings\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-primary mb-1\",\n                                        children: [\n                                            \"$\",\n                                            calculateROI(currentMetrics.costSavings),\n                                            \"M\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-text-secondary\",\n                                        children: \"Reduced operational costs and bad debt\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/50 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-text-primary mb-2\",\n                                        children: \"Revenue Increase\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-secondary mb-1\",\n                                        children: [\n                                            \"+\",\n                                            currentMetrics.revenueIncrease,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-text-secondary\",\n                                        children: \"Higher user retention and volume\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/50 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-text-primary mb-2\",\n                                        children: \"Total ROI\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-accent mb-1\",\n                                        children: selectedTimeframe === \"12-months\" ? \"500%\" : \"750%\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-text-secondary\",\n                                        children: \"Return on investment\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                lineNumber: 255,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface border border-border-color rounded-xl p-4 sm:p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-text-primary mb-6 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"w-5 h-5 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 11\n                            }, this),\n                            \"Success Stories & Case Studies\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-surface-light rounded-lg p-4 border border-border-color\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-6 h-6 text-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-text-primary\",\n                                                        children: \"DeFi Protocol Alpha\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-text-secondary\",\n                                                        children: \"$1.2B TVL, 50K+ users\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-text-secondary\",\n                                                        children: \"Liquidation Reduction:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-secondary\",\n                                                        children: \"-45%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 311,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-text-secondary\",\n                                                        children: \"Bad Debt Reduction:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-secondary\",\n                                                        children: \"-62%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-text-secondary\",\n                                                        children: \"Revenue Increase:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-secondary\",\n                                                        children: \"+32%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-text-secondary\",\n                                                        children: \"Implementation Time:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-text-primary\",\n                                                        children: \"8 months\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 p-3 bg-primary/5 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-text-secondary italic\",\n                                                children: '\"zScore implementation reduced our liquidation events by 45% and significantly improved our risk assessment capabilities. The ROI was evident within 6 months.\"'\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-text-muted mt-2\",\n                                                children: \"- CTO, DeFi Protocol Alpha\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-surface-light rounded-lg p-4 border border-border-color\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-secondary/20 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"w-6 h-6 text-secondary\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-text-primary\",\n                                                        children: \"Lending Platform Beta\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-text-secondary\",\n                                                        children: \"$800M TVL, 35K+ users\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-text-secondary\",\n                                                        children: \"User Retention:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-secondary\",\n                                                        children: \"+38%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-text-secondary\",\n                                                        children: \"Processing Speed:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-secondary\",\n                                                        children: \"95% faster\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 352,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 350,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-text-secondary\",\n                                                        children: \"Cost Reduction:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-secondary\",\n                                                        children: \"-55%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-text-secondary\",\n                                                        children: \"ROI Achievement:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-text-primary\",\n                                                        children: \"10 months\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 p-3 bg-secondary/5 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-text-secondary italic\",\n                                                children: '\"The automated risk assessment and cross-chain reputation tracking transformed our lending operations. User experience improved dramatically.\"'\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-text-muted mt-2\",\n                                                children: \"- Head of Risk, Lending Platform Beta\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                lineNumber: 292,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface border border-border-color rounded-xl p-4 sm:p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-text-primary mb-6 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-5 h-5 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 11\n                            }, this),\n                            \"Market Opportunity & Growth Potential\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 376,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-primary mb-2\",\n                                        children: _data_valueData__WEBPACK_IMPORTED_MODULE_2__.marketOpportunity.totalAddressableMarket\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-text-primary mb-1\",\n                                        children: \"Total Addressable Market\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-text-secondary\",\n                                        children: \"DeFi lending protocols globally\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-secondary mb-2\",\n                                        children: [\n                                            _data_valueData__WEBPACK_IMPORTED_MODULE_2__.marketOpportunity.marketGrowthRate,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-text-primary mb-1\",\n                                        children: \"Annual Growth Rate\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-text-secondary\",\n                                        children: \"Year-over-year expansion\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 386,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-accent mb-2\",\n                                        children: _data_valueData__WEBPACK_IMPORTED_MODULE_2__.marketOpportunity.protocolsAddressed\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-text-primary mb-1\",\n                                        children: \"Protocols Addressable\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-text-secondary\",\n                                        children: \"Potential integration targets\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 391,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-primary mb-2\",\n                                        children: _data_valueData__WEBPACK_IMPORTED_MODULE_2__.marketOpportunity.potentialRevenue\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-text-primary mb-1\",\n                                        children: \"Revenue Potential\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-text-secondary\",\n                                        children: \"5-year projection\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 380,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                lineNumber: 375,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface border border-border-color rounded-xl p-4 sm:p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-text-primary mb-6 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-5 h-5 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 407,\n                                columnNumber: 11\n                            }, this),\n                            \"Risk Mitigation & Compliance Benefits\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 406,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                        children: _data_valueData__WEBPACK_IMPORTED_MODULE_2__.riskMitigationBenefits.map((risk, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-surface-light rounded-lg p-4 border border-border-color\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start justify-between mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-text-primary\",\n                                                children: risk.risk\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 414,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(risk.impact === \"High\" ? \"bg-red-100 text-red-700\" : risk.impact === \"Medium\" ? \"bg-yellow-100 text-yellow-700\" : \"bg-green-100 text-green-700\"),\n                                                        children: [\n                                                            risk.impact,\n                                                            \" Impact\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(risk.probability === \"High\" ? \"bg-red-100 text-red-700\" : risk.probability === \"Medium\" ? \"bg-yellow-100 text-yellow-700\" : \"bg-green-100 text-green-700\"),\n                                                        children: [\n                                                            risk.probability,\n                                                            \" Probability\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 415,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 413,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-text-secondary mb-3\",\n                                        children: risk.mitigation\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-text-secondary\",\n                                                children: \"Risk Reduction:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg font-bold text-secondary\",\n                                                children: risk.reduction\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 410,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                lineNumber: 405,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface border border-border-color rounded-xl p-4 sm:p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-text-primary mb-6 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"w-5 h-5 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 11\n                            }, this),\n                            \"Industry Performance Comparison\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 444,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium text-text-primary mb-2\",\n                                                children: \"Liquidation Rate\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-lg font-bold text-red-500\",\n                                                                children: [\n                                                                    _data_valueData__WEBPACK_IMPORTED_MODULE_2__.financialProjections.industryAverages.liquidationRate,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                                lineNumber: 454,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-text-muted\",\n                                                                children: \"Industry Avg\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                                lineNumber: 455,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-4 h-4 text-secondary rotate-180\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 457,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-lg font-bold text-secondary\",\n                                                                children: [\n                                                                    _data_valueData__WEBPACK_IMPORTED_MODULE_2__.financialProjections.withZScore.liquidationRate,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                                lineNumber: 459,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-text-muted\",\n                                                                children: \"With zScore\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                                lineNumber: 460,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 458,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-secondary\",\n                                        children: [\n                                            _data_valueData__WEBPACK_IMPORTED_MODULE_2__.financialProjections.improvements.liquidationReduction.toFixed(1),\n                                            \"% improvement\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 464,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 449,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium text-text-primary mb-2\",\n                                                children: \"Bad Debt Ratio\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 471,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-lg font-bold text-red-500\",\n                                                                children: [\n                                                                    _data_valueData__WEBPACK_IMPORTED_MODULE_2__.financialProjections.industryAverages.badDebtRatio,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                                lineNumber: 474,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-text-muted\",\n                                                                children: \"Industry Avg\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                                lineNumber: 475,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 473,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-4 h-4 text-secondary rotate-180\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 477,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-lg font-bold text-secondary\",\n                                                                children: [\n                                                                    _data_valueData__WEBPACK_IMPORTED_MODULE_2__.financialProjections.withZScore.badDebtRatio,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                                lineNumber: 479,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-text-muted\",\n                                                                children: \"With zScore\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                                lineNumber: 480,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 478,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-secondary\",\n                                        children: [\n                                            _data_valueData__WEBPACK_IMPORTED_MODULE_2__.financialProjections.improvements.badDebtReduction.toFixed(1),\n                                            \"% improvement\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 484,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 469,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium text-text-primary mb-2\",\n                                                children: \"User Churn Rate\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 491,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-lg font-bold text-red-500\",\n                                                                children: [\n                                                                    _data_valueData__WEBPACK_IMPORTED_MODULE_2__.financialProjections.industryAverages.userChurnRate,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                                lineNumber: 494,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-text-muted\",\n                                                                children: \"Industry Avg\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                                lineNumber: 495,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 493,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-4 h-4 text-secondary rotate-180\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 497,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-lg font-bold text-secondary\",\n                                                                children: [\n                                                                    _data_valueData__WEBPACK_IMPORTED_MODULE_2__.financialProjections.withZScore.userChurnRate,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                                lineNumber: 499,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-text-muted\",\n                                                                children: \"With zScore\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                                lineNumber: 500,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 498,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 492,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 490,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-secondary\",\n                                        children: [\n                                            _data_valueData__WEBPACK_IMPORTED_MODULE_2__.financialProjections.improvements.userRetentionIncrease.toFixed(1),\n                                            \"% improvement\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 504,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 489,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium text-text-primary mb-2\",\n                                                children: \"Operational Costs\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 511,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-lg font-bold text-red-500\",\n                                                                children: [\n                                                                    _data_valueData__WEBPACK_IMPORTED_MODULE_2__.financialProjections.industryAverages.operationalCosts,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                                lineNumber: 514,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-text-muted\",\n                                                                children: \"Industry Avg\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                                lineNumber: 515,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 513,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-4 h-4 text-secondary rotate-180\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 517,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-lg font-bold text-secondary\",\n                                                                children: [\n                                                                    _data_valueData__WEBPACK_IMPORTED_MODULE_2__.financialProjections.withZScore.operationalCosts,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                                lineNumber: 519,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-text-muted\",\n                                                                children: \"With zScore\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                                lineNumber: 520,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 518,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 512,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-secondary\",\n                                        children: [\n                                            _data_valueData__WEBPACK_IMPORTED_MODULE_2__.financialProjections.improvements.costReduction.toFixed(1),\n                                            \"% improvement\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 524,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 509,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 448,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                lineNumber: 443,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-primary to-secondary rounded-xl p-6 text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold mb-2\",\n                            children: \"Ready to Transform Your Lending Protocol?\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                            lineNumber: 534,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/90 mb-6 max-w-2xl mx-auto\",\n                            children: \"Join leading DeFi protocols that have already implemented zScore and achieved significant ROI improvements. Start your journey to better risk management today.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                            lineNumber: 535,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"px-6 py-3 bg-white text-primary font-semibold rounded-lg hover:bg-white/90 transition-colors\",\n                                    children: \"Schedule Demo\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                    lineNumber: 540,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"px-6 py-3 border border-white/30 text-white font-semibold rounded-lg hover:bg-white/10 transition-colors\",\n                                    children: \"Download Full Report\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                    lineNumber: 543,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                            lineNumber: 539,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                    lineNumber: 533,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                lineNumber: 532,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, this);\n}\n_s(LendingValue, \"thRYqejP5hfDHh7aLeKKXwezsMI=\");\n_c = LendingValue;\nvar _c;\n$RefreshReg$(_c, \"LendingValue\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/lending/Value.tsx\n"));

/***/ })

});