"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/TabSystem.tsx":
/*!**************************************!*\
  !*** ./src/components/TabSystem.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TabSystem; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _lending_RealUsecase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lending/RealUsecase */ \"(app-pages-browser)/./src/components/lending/RealUsecase.tsx\");\n/* harmony import */ var _lending_Global__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lending/Global */ \"(app-pages-browser)/./src/components/lending/Global.tsx\");\n/* harmony import */ var _lending_Ideal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lending/Ideal */ \"(app-pages-browser)/./src/components/lending/Ideal.tsx\");\n/* harmony import */ var _lending_Value__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./lending/Value */ \"(app-pages-browser)/./src/components/lending/Value.tsx\");\n/* harmony import */ var _ComingSoon__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ComingSoon */ \"(app-pages-browser)/./src/components/ComingSoon.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst tabs = [\n    {\n        id: \"real-usecase\",\n        name: \"Real-usecase\",\n        description: \"Live data analysis\"\n    },\n    {\n        id: \"global\",\n        name: \"Global\",\n        description: \"Worldwide metrics\"\n    },\n    {\n        id: \"ideal\",\n        name: \"Ideal usecases\",\n        description: \"Best practices\"\n    },\n    {\n        id: \"value\",\n        name: \"Value\",\n        description: \"ROI & Business Impact\"\n    }\n];\nfunction TabSystem(param) {\n    let { selectedSector, selectedTab, onTabChange } = param;\n    const renderContent = ()=>{\n        if (selectedSector === \"lending\") {\n            switch(selectedTab){\n                case \"real-usecase\":\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lending_RealUsecase__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 18\n                    }, this);\n                case \"global\":\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lending_Global__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 18\n                    }, this);\n                case \"ideal\":\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lending_Ideal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 18\n                    }, this);\n                case \"value\":\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lending_Value__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 18\n                    }, this);\n                default:\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lending_RealUsecase__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 18\n                    }, this);\n            }\n        }\n        // For other sectors, show coming soon\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ComingSoon__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            sector: selectedSector\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n            lineNumber: 42,\n            columnNumber: 12\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface border-b border-border-color px-4 sm:px-6 py-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg sm:text-xl font-semibold text-text-primary capitalize\",\n                                    children: [\n                                        selectedSector,\n                                        \" Analytics\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-text-secondary hidden sm:block\",\n                                    children: [\n                                        \"Comprehensive analysis and insights for \",\n                                        selectedSector,\n                                        \" use cases\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-1 bg-surface-light rounded-lg p-1 overflow-x-auto\",\n                        children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>onTabChange(tab.id),\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"flex-1 min-w-0 px-2 sm:px-4 py-3 rounded-md text-xs sm:text-sm font-medium transition-all duration-200\", \"hover:bg-surface hover:text-text-primary whitespace-nowrap\", selectedTab === tab.id ? \"bg-primary text-white shadow-sm\" : \"text-text-secondary\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium\",\n                                            children: tab.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs opacity-75 mt-1 hidden sm:block\",\n                                            children: tab.description\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 15\n                                }, this)\n                            }, tab.id, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-auto\",\n                children: renderContent()\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/TabSystem.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n_c = TabSystem;\nvar _c;\n$RefreshReg$(_c, \"TabSystem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TabSystem.tsx\n"));

/***/ })

});