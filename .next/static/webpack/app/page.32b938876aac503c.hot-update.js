"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/data/valueData.ts":
/*!*******************************!*\
  !*** ./src/data/valueData.ts ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   caseStudies: function() { return /* binding */ caseStudies; },\n/* harmony export */   competitiveAdvantages: function() { return /* binding */ competitiveAdvantages; },\n/* harmony export */   financialProjections: function() { return /* binding */ financialProjections; },\n/* harmony export */   implementationPhases: function() { return /* binding */ implementationPhases; },\n/* harmony export */   marketOpportunity: function() { return /* binding */ marketOpportunity; },\n/* harmony export */   protocolSizes: function() { return /* binding */ protocolSizes; },\n/* harmony export */   riskMitigationBenefits: function() { return /* binding */ riskMitigationBenefits; },\n/* harmony export */   roiMetrics: function() { return /* binding */ roiMetrics; },\n/* harmony export */   technologyAdvantages: function() { return /* binding */ technologyAdvantages; }\n/* harmony export */ });\n// ROI and Value proposition data for zScore implementation\n// ROI metrics by timeframe - ACTUAL DATA from Zeru Finance zScore ROI Impact Report (Jan 2024 – May 2025)\nconst roiMetrics = {\n    \"12-months\": {\n        liquidationReduction: 37,\n        badDebtReduction: 36,\n        revenueIncrease: 12,\n        costSavings: 4.5,\n        userRetention: 15,\n        operationalEfficiency: 30 // Loan Volume Growth improved by 8% points (22% → 30%)\n    },\n    \"17-months\": {\n        liquidationReduction: 37,\n        badDebtReduction: 36,\n        revenueIncrease: 12,\n        costSavings: 4.5,\n        userRetention: 15,\n        operationalEfficiency: 30 // Consistent with report findings\n    }\n};\n// Protocol size configurations\nconst protocolSizes = {\n    small: {\n        tvl: \"50M\",\n        users: \"5K\",\n        multiplier: 0.5\n    },\n    medium: {\n        tvl: \"500M\",\n        users: \"25K\",\n        multiplier: 1.0\n    },\n    large: {\n        tvl: \"2B\",\n        users: \"100K\",\n        multiplier: 2.5\n    }\n};\n// Implementation phases with detailed ROI projections\nconst implementationPhases = [\n    {\n        phase: \"Phase 1: Integration\",\n        duration: \"2-3 months\",\n        investment: \"$150K\",\n        roi: \"Break-even in 4 months\",\n        status: \"immediate\",\n        benefits: [\n            \"Basic zScore integration\",\n            \"Risk categorization\",\n            \"Initial liquidation reduction\",\n            \"Automated risk scoring\"\n        ]\n    },\n    {\n        phase: \"Phase 2: Optimization\",\n        duration: \"3-4 months\",\n        investment: \"$200K\",\n        roi: \"300% ROI by month 8\",\n        status: \"short-term\",\n        benefits: [\n            \"Advanced risk models\",\n            \"Dynamic pricing\",\n            \"Cross-chain reputation\",\n            \"Real-time monitoring\"\n        ]\n    },\n    {\n        phase: \"Phase 3: Advanced Features\",\n        duration: \"2-3 months\",\n        investment: \"$100K\",\n        roi: \"500% ROI by month 12\",\n        status: \"long-term\",\n        benefits: [\n            \"AI-powered predictions\",\n            \"Automated recommendations\",\n            \"Governance integration\",\n            \"Advanced analytics\"\n        ]\n    }\n];\n// Competitive advantages analysis\nconst competitiveAdvantages = [\n    {\n        feature: \"Real-time Risk Assessment\",\n        traditional: \"Manual review, 24-48h delay\",\n        withZScore: \"Instant automated assessment\",\n        improvement: \"95% faster processing\"\n    },\n    {\n        feature: \"Liquidation Prediction\",\n        traditional: \"Reactive, post-event analysis\",\n        withZScore: \"Proactive, 24-48h advance warning\",\n        improvement: \"42% reduction in liquidations\"\n    },\n    {\n        feature: \"Cross-chain Reputation\",\n        traditional: \"Siloed, single-chain data\",\n        withZScore: \"Unified, multi-chain reputation\",\n        improvement: \"65% better risk assessment\"\n    },\n    {\n        feature: \"User Experience\",\n        traditional: \"Complex, manual processes\",\n        withZScore: \"Seamless, automated onboarding\",\n        improvement: \"35% higher user retention\"\n    },\n    {\n        feature: \"Bad Debt Management\",\n        traditional: \"Reactive liquidations\",\n        withZScore: \"Predictive risk mitigation\",\n        improvement: \"65% reduction in bad debt\"\n    },\n    {\n        feature: \"Operational Costs\",\n        traditional: \"High manual oversight\",\n        withZScore: \"Automated risk management\",\n        improvement: \"55% cost reduction\"\n    }\n];\n// Success stories and case studies\nconst caseStudies = [\n    {\n        name: \"DeFi Protocol Alpha\",\n        tvl: \"$1.2B\",\n        users: \"50K+\",\n        liquidationReduction: 45,\n        badDebtReduction: 62,\n        revenueIncrease: 32,\n        implementationTime: \"8 months\",\n        testimonial: \"zScore implementation reduced our liquidation events by 45% and significantly improved our risk assessment capabilities. The ROI was evident within 6 months.\",\n        author: \"CTO, DeFi Protocol Alpha\",\n        icon: \"zap\"\n    },\n    {\n        name: \"Lending Platform Beta\",\n        tvl: \"$800M\",\n        users: \"35K+\",\n        liquidationReduction: 38,\n        badDebtReduction: 55,\n        revenueIncrease: 28,\n        implementationTime: \"10 months\",\n        testimonial: \"The automated risk assessment and cross-chain reputation tracking transformed our lending operations. User experience improved dramatically.\",\n        author: \"Head of Risk, Lending Platform Beta\",\n        icon: \"shield\"\n    },\n    {\n        name: \"Multi-Chain Lender Gamma\",\n        tvl: \"$2.1B\",\n        users: \"75K+\",\n        liquidationReduction: 52,\n        badDebtReduction: 71,\n        revenueIncrease: 41,\n        implementationTime: \"6 months\",\n        testimonial: \"Cross-chain reputation tracking was a game-changer. We saw immediate improvements in risk assessment accuracy and user trust.\",\n        author: \"CEO, Multi-Chain Lender Gamma\",\n        icon: \"target\"\n    }\n];\n// Key financial metrics and projections\nconst financialProjections = {\n    industryAverages: {\n        liquidationRate: 8.5,\n        badDebtRatio: 4.2,\n        userChurnRate: 25,\n        operationalCosts: 15\n    },\n    withZScore: {\n        liquidationRate: 4.9,\n        badDebtRatio: 1.5,\n        userChurnRate: 16,\n        operationalCosts: 6.8\n    },\n    improvements: {\n        liquidationReduction: 42.4,\n        badDebtReduction: 64.3,\n        userRetentionIncrease: 36.0,\n        costReduction: 54.7\n    }\n};\n// Market opportunity and addressable market\nconst marketOpportunity = {\n    totalAddressableMarket: \"$18.7B\",\n    servicableAddressableMarket: \"$8.2B\",\n    servicableObtainableMarket: \"$1.4B\",\n    marketGrowthRate: 23.5,\n    protocolsAddressed: 150,\n    potentialRevenue: \"$420M\"\n};\n// Risk mitigation benefits\nconst riskMitigationBenefits = [\n    {\n        risk: \"Liquidation Cascade Events\",\n        impact: \"High\",\n        probability: \"Medium\",\n        mitigation: \"Real-time risk monitoring and early warning systems\",\n        reduction: \"65%\"\n    },\n    {\n        risk: \"Bad Debt Accumulation\",\n        impact: \"High\",\n        probability: \"High\",\n        mitigation: \"Predictive modeling and proactive interventions\",\n        reduction: \"72%\"\n    },\n    {\n        risk: \"Regulatory Compliance\",\n        impact: \"Medium\",\n        probability: \"Low\",\n        mitigation: \"Transparent and auditable risk assessment\",\n        reduction: \"85%\"\n    },\n    {\n        risk: \"User Trust Issues\",\n        impact: \"Medium\",\n        probability: \"Medium\",\n        mitigation: \"Transparent reputation scoring and explanations\",\n        reduction: \"45%\"\n    }\n];\n// Technology advantages\nconst technologyAdvantages = [\n    {\n        feature: \"EigenLayer Integration\",\n        benefit: \"Decentralized and secure reputation system\",\n        impact: \"Enhanced trust and security\"\n    },\n    {\n        feature: \"Multi-chain Support\",\n        benefit: \"Unified reputation across all major chains\",\n        impact: \"Comprehensive risk assessment\"\n    },\n    {\n        feature: \"Real-time Processing\",\n        benefit: \"Instant risk score updates\",\n        impact: \"Immediate risk mitigation\"\n    },\n    {\n        feature: \"AI-Powered Analytics\",\n        benefit: \"Advanced pattern recognition\",\n        impact: \"Predictive risk management\"\n    },\n    {\n        feature: \"Open Source\",\n        benefit: \"Transparent and auditable algorithms\",\n        impact: \"Community trust and adoption\"\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/data/valueData.ts\n"));

/***/ })

});