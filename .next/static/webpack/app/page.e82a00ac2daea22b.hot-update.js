"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/lending/Value.tsx":
/*!******************************************!*\
  !*** ./src/components/lending/Value.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LendingValue; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,Award,BarChart3,Calculator,CheckCircle,DollarSign,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,Award,BarChart3,Calculator,CheckCircle,DollarSign,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,Award,BarChart3,Calculator,CheckCircle,DollarSign,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,Award,BarChart3,Calculator,CheckCircle,DollarSign,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,Award,BarChart3,Calculator,CheckCircle,DollarSign,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,Award,BarChart3,Calculator,CheckCircle,DollarSign,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calculator.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,Award,BarChart3,Calculator,CheckCircle,DollarSign,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,Award,BarChart3,Calculator,CheckCircle,DollarSign,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,Award,BarChart3,Calculator,CheckCircle,DollarSign,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,Award,BarChart3,Calculator,CheckCircle,DollarSign,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,Award,BarChart3,Calculator,CheckCircle,DollarSign,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _data_valueData__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/valueData */ \"(app-pages-browser)/./src/data/valueData.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction LendingValue() {\n    _s();\n    const [selectedTimeframe, setSelectedTimeframe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"17-months-actual\");\n    const [protocolSize, setProtocolSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"medium\");\n    const currentMetrics = _data_valueData__WEBPACK_IMPORTED_MODULE_2__.roiMetrics[selectedTimeframe];\n    const currentSize = _data_valueData__WEBPACK_IMPORTED_MODULE_2__.protocolSizes[protocolSize];\n    // Calculate ROI based on protocol size\n    const calculateROI = (baseValue)=>{\n        return (baseValue * currentSize.multiplier).toFixed(1);\n    };\n    const businessImpacts = [\n        {\n            title: \"Liquidation Risk Reduction\",\n            value: \"\".concat(currentMetrics.liquidationReduction, \"%\"),\n            description: \"Decrease in unexpected liquidations\",\n            icon: _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            color: \"text-green-500\",\n            bgColor: \"bg-green-50\",\n            trend: \"+15% vs industry average\"\n        },\n        {\n            title: \"Bad Debt Mitigation\",\n            value: \"\".concat(currentMetrics.badDebtReduction, \"%\"),\n            description: \"Reduction in protocol bad debt\",\n            icon: _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            color: \"text-blue-500\",\n            bgColor: \"bg-blue-50\",\n            trend: \"Industry leading performance\"\n        },\n        {\n            title: \"Revenue Growth\",\n            value: \"\".concat(currentMetrics.revenueIncrease, \"%\"),\n            description: \"Increase in protocol revenue\",\n            icon: _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            color: \"text-purple-500\",\n            bgColor: \"bg-purple-50\",\n            trend: \"$\".concat(calculateROI(currentMetrics.costSavings), \"M additional revenue\")\n        },\n        {\n            title: \"User Retention\",\n            value: \"\".concat(currentMetrics.userRetention, \"%\"),\n            description: \"Improvement in user retention\",\n            icon: _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            color: \"text-orange-500\",\n            bgColor: \"bg-orange-50\",\n            trend: \"Higher lifetime value\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-4 sm:p-6 space-y-4 sm:space-y-6 bg-background min-h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface border border-border-color rounded-xl p-4 sm:p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col lg:flex-row lg:items-start lg:justify-between space-y-4 lg:space-y-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl sm:text-2xl font-bold text-text-primary mb-2\",\n                                    children: \"zScore ROI Impact Report\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-text-secondary mb-4 text-sm sm:text-base\",\n                                    children: \"Comprehensive analysis of financial benefits and business impact from implementing Zeru Finance zScore reputation system in DeFi lending protocols.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-3 py-1 bg-primary/10 text-primary rounded-full text-xs font-medium\",\n                                            children: \"Jan 2024 - May 2025\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-3 py-1 bg-secondary/10 text-secondary rounded-full text-xs font-medium\",\n                                            children: \"Live Data Analysis\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-3 py-1 bg-accent/10 text-accent rounded-full text-xs font-medium\",\n                                            children: \"Proven ROI\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-primary/10 to-secondary/10 border border-primary/20 rounded-lg p-4 lg:ml-4 flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-primary mb-1\",\n                                        children: \"$4.5M\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-text-secondary\",\n                                        children: \"Annual Profit Lift\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-text-muted mt-1\",\n                                        children: \"Proven Results\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-green-50 to-blue-50 border-2 border-green-200 rounded-xl p-4 sm:p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-8 h-8 text-green-600 mr-3\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-bold text-green-800\",\n                                children: \"ACTUAL PROVEN RESULTS\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-green-700 font-medium mb-2\",\n                                children: [\n                                    \"All metrics below are based on \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"real data\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 44\n                                    }, this),\n                                    \" from the Zeru Finance zScore ROI Impact Report\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-green-600 text-sm\",\n                                children: [\n                                    \"Analysis Period: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"January 2024 - May 2025\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 30\n                                    }, this),\n                                    \" | Sample Size: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"10,000+ borrowers\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 26\n                                    }, this),\n                                    \" | Statistical Significance: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"95%+ confidence\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 39\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/70 rounded-lg p-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-600\",\n                                        children: \"37%\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-green-700\",\n                                        children: \"Liquidation Reduction\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-green-600\",\n                                        children: \"10.2% → 6.4%\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/70 rounded-lg p-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-600\",\n                                        children: \"36%\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-green-700\",\n                                        children: \"Bad Debt Reduction\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-green-600\",\n                                        children: \"0.50% → 0.32%\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/70 rounded-lg p-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-600\",\n                                        children: \"$900\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-green-700\",\n                                        children: \"Revenue per User\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-green-600\",\n                                        children: \"+12% increase\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/70 rounded-lg p-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-600\",\n                                        children: \"$200M\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-green-700\",\n                                        children: \"Unlocked Capacity\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-green-600\",\n                                        children: \"LTV: 67% → 80%\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface border border-border-color rounded-xl p-4 sm:p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-text-primary mb-4 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-5 h-5 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, this),\n                            \"ROI Calculator\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-text-primary mb-2\",\n                                        children: \"Timeframe\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: selectedTimeframe,\n                                        onChange: (e)=>setSelectedTimeframe(e.target.value),\n                                        className: \"w-full p-3 border border-border-color rounded-lg bg-surface-light focus:ring-2 focus:ring-primary focus:border-transparent\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"12-months-projected\",\n                                                children: \"12 Months (Projected)\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"17-months-actual\",\n                                                children: \"17 Months (ACTUAL PROVEN DATA - Jan 2024 to May 2025)\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-text-primary mb-2\",\n                                        children: \"Protocol Size\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: protocolSize,\n                                        onChange: (e)=>setProtocolSize(e.target.value),\n                                        className: \"w-full p-3 border border-border-color rounded-lg bg-surface-light focus:ring-2 focus:ring-primary focus:border-transparent\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"small\",\n                                                children: \"Small ($50M TVL, 5K users)\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"medium\",\n                                                children: \"Medium ($500M TVL, 25K users)\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"large\",\n                                                children: \"Large ($2B+ TVL, 100K+ users)\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                lineNumber: 156,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6\",\n                children: businessImpacts.map((impact, index)=>{\n                    const Icon = impact.icon;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-surface border border-border-color rounded-xl p-4 sm:p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 \".concat(impact.bgColor, \" rounded-lg flex items-center justify-center\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: \"w-6 h-6 \".concat(impact.color)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-4 h-4 text-secondary\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-semibold text-text-primary mb-2\",\n                                children: impact.title\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-text-primary mb-1\",\n                                children: impact.value\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-text-secondary mb-2\",\n                                children: impact.description\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-secondary font-medium\",\n                                children: impact.trend\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                lineNumber: 193,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface border border-border-color rounded-xl p-4 sm:p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-text-primary mb-6 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"w-5 h-5 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, this),\n                            \"Competitive Advantage Analysis\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        className: \"border-b border-border-color\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 font-semibold text-text-primary\",\n                                                children: \"Feature\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 font-semibold text-text-primary\",\n                                                children: \"Traditional Approach\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 font-semibold text-text-primary\",\n                                                children: \"With zScore\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 font-semibold text-text-primary\",\n                                                children: \"Improvement\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: _data_valueData__WEBPACK_IMPORTED_MODULE_2__.competitiveAdvantages.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"border-b border-border-color/50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-4 px-4 font-medium text-text-primary\",\n                                                    children: item.feature\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-4 px-4 text-text-secondary\",\n                                                    children: item.traditional\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-4 px-4 text-text-secondary\",\n                                                    children: item.withZScore\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-4 px-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 bg-secondary/10 text-secondary rounded-full text-sm font-medium\",\n                                                        children: item.improvement\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                lineNumber: 214,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-primary/5 to-secondary/5 border border-primary/20 rounded-xl p-4 sm:p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-text-primary mb-6 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"w-5 h-5 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 11\n                            }, this),\n                            \"Lending $ Impact Waterfall (From ROI Report Figure 3)\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/70 rounded-lg p-6 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-text-primary mb-4 text-center\",\n                                children: \"Net Annual Profit: $98M → $102.5M (+$4.5M)\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-semibold text-green-800\",\n                                                        children: \"1. Avoided Bad Debt Losses\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-green-600\",\n                                                        children: \"Risk reduction from filtering low zScore borrowers\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xl font-bold text-green-600\",\n                                                children: \"+$1.5M\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-semibold text-red-800\",\n                                                        children: \"2. Reduced Risky-Loan Interest\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-red-600\",\n                                                        children: \"Revenue lost from loans not granted to low zScores\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xl font-bold text-red-600\",\n                                                children: \"-$5.0M\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-semibold text-blue-800\",\n                                                        children: \"3. Additional Interest from High-zScore Borrowers\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-blue-600\",\n                                                        children: \"Higher LTV (67% → 80%) and new loans to creditworthy users\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xl font-bold text-blue-600\",\n                                                children: \"+$8.0M\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-t-2 border-gray-300 pt-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-3 bg-primary/10 rounded-lg border border-primary/30\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-bold text-primary text-lg\",\n                                                            children: \"NET RESULT\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                            lineNumber: 285,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-text-secondary\",\n                                                            children: \"Total annual profit improvement\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-primary\",\n                                                    children: \"+$4.5M\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/50 rounded-lg p-4 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-primary mb-1\",\n                                        children: \"$200M\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-text-primary mb-1\",\n                                        children: \"Unlocked Capacity\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-text-secondary\",\n                                        children: \"Additional borrowing on Ethereum L1\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/50 rounded-lg p-4 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-secondary mb-1\",\n                                        children: \"10,000+\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-text-primary mb-1\",\n                                        children: \"Borrowers Analyzed\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-text-secondary\",\n                                        children: \"Statistical significance p<0.001\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/50 rounded-lg p-4 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-accent mb-1\",\n                                        children: \"$1.5B\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-text-primary mb-1\",\n                                        children: \"Collateral Analyzed\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-text-secondary\",\n                                        children: \"Ethereum & L2 aggregate\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                lineNumber: 248,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface border border-border-color rounded-xl p-4 sm:p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-text-primary mb-6 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"w-5 h-5 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 11\n                            }, this),\n                            \"Success Stories & Case Studies\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6\",\n                        children: _data_valueData__WEBPACK_IMPORTED_MODULE_2__.caseStudies.map((study, index)=>{\n                            const IconComponent = study.icon === \"zap\" ? _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"] : study.icon === \"shield\" ? _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"] : _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n                            const colorClass = index === 0 ? \"primary\" : index === 1 ? \"secondary\" : \"accent\";\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-surface-light rounded-lg p-4 border border-border-color\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-\".concat(colorClass, \"/20 rounded-lg flex items-center justify-center\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                    className: \"w-6 h-6 text-\".concat(colorClass)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-text-primary\",\n                                                        children: study.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-text-secondary\",\n                                                        children: [\n                                                            study.tvl,\n                                                            \" TVL, \",\n                                                            study.users,\n                                                            \" users\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-text-secondary\",\n                                                        children: \"Liquidation Reduction:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-secondary\",\n                                                        children: [\n                                                            \"-\",\n                                                            study.liquidationReduction,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-text-secondary\",\n                                                        children: \"Bad Debt Reduction:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-secondary\",\n                                                        children: [\n                                                            \"-\",\n                                                            study.badDebtReduction,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-text-secondary\",\n                                                        children: \"Revenue Increase:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-secondary\",\n                                                        children: [\n                                                            \"+\",\n                                                            study.revenueIncrease,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-text-secondary\",\n                                                        children: \"Implementation Time:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-text-primary\",\n                                                        children: study.implementationTime\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 p-3 bg-\".concat(colorClass, \"/5 rounded-lg\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-text-secondary italic\",\n                                                children: [\n                                                    '\"',\n                                                    study.testimonial,\n                                                    '\"'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-text-muted mt-2\",\n                                                children: [\n                                                    \"- \",\n                                                    study.author\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                lineNumber: 314,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface border border-border-color rounded-xl p-4 sm:p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-text-primary mb-6 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-5 h-5 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 11\n                            }, this),\n                            \"Market Opportunity & Growth Potential\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 367,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-primary mb-2\",\n                                        children: _data_valueData__WEBPACK_IMPORTED_MODULE_2__.marketOpportunity.totalAddressableMarket\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-text-primary mb-1\",\n                                        children: \"Total Addressable Market\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-text-secondary\",\n                                        children: \"DeFi lending protocols globally\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-secondary mb-2\",\n                                        children: [\n                                            _data_valueData__WEBPACK_IMPORTED_MODULE_2__.marketOpportunity.marketGrowthRate,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-text-primary mb-1\",\n                                        children: \"Annual Growth Rate\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-text-secondary\",\n                                        children: \"Year-over-year expansion\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-accent mb-2\",\n                                        children: _data_valueData__WEBPACK_IMPORTED_MODULE_2__.marketOpportunity.protocolsAddressed\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-text-primary mb-1\",\n                                        children: \"Protocols Addressable\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-text-secondary\",\n                                        children: \"Potential integration targets\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-primary mb-2\",\n                                        children: _data_valueData__WEBPACK_IMPORTED_MODULE_2__.marketOpportunity.potentialRevenue\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-text-primary mb-1\",\n                                        children: \"Revenue Potential\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-text-secondary\",\n                                        children: \"5-year projection\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 371,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                lineNumber: 366,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface border border-border-color rounded-xl p-4 sm:p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-text-primary mb-6 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-5 h-5 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 11\n                            }, this),\n                            \"Risk Mitigation & Compliance Benefits\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 397,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                        children: _data_valueData__WEBPACK_IMPORTED_MODULE_2__.riskMitigationBenefits.map((risk, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-surface-light rounded-lg p-4 border border-border-color\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start justify-between mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-text-primary\",\n                                                children: risk.risk\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(risk.impact === \"High\" ? \"bg-red-100 text-red-700\" : risk.impact === \"Medium\" ? \"bg-yellow-100 text-yellow-700\" : \"bg-green-100 text-green-700\"),\n                                                        children: [\n                                                            risk.impact,\n                                                            \" Impact\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 407,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(risk.probability === \"High\" ? \"bg-red-100 text-red-700\" : risk.probability === \"Medium\" ? \"bg-yellow-100 text-yellow-700\" : \"bg-green-100 text-green-700\"),\n                                                        children: [\n                                                            risk.probability,\n                                                            \" Probability\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 414,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-text-secondary mb-3\",\n                                        children: risk.mitigation\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-text-secondary\",\n                                                children: \"Risk Reduction:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg font-bold text-secondary\",\n                                                children: risk.reduction\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 424,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 401,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                lineNumber: 396,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface border border-border-color rounded-xl p-4 sm:p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-text-primary mb-6 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"w-5 h-5 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 436,\n                                columnNumber: 11\n                            }, this),\n                            \"Industry Performance Comparison\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 435,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium text-text-primary mb-2\",\n                                                children: \"Liquidation Rate\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-lg font-bold text-red-500\",\n                                                                children: [\n                                                                    _data_valueData__WEBPACK_IMPORTED_MODULE_2__.financialProjections.industryAverages.liquidationRate,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                                lineNumber: 445,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-text-muted\",\n                                                                children: \"Industry Avg\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                                lineNumber: 446,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-4 h-4 text-secondary rotate-180\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-lg font-bold text-secondary\",\n                                                                children: [\n                                                                    _data_valueData__WEBPACK_IMPORTED_MODULE_2__.financialProjections.withZScore.liquidationRate,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                                lineNumber: 450,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-text-muted\",\n                                                                children: \"With zScore\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                                lineNumber: 451,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-secondary\",\n                                        children: [\n                                            _data_valueData__WEBPACK_IMPORTED_MODULE_2__.financialProjections.improvements.liquidationReduction.toFixed(1),\n                                            \"% improvement\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 440,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium text-text-primary mb-2\",\n                                                children: \"Bad Debt Ratio\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-lg font-bold text-red-500\",\n                                                                children: [\n                                                                    _data_valueData__WEBPACK_IMPORTED_MODULE_2__.financialProjections.industryAverages.badDebtRatio,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                                lineNumber: 465,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-text-muted\",\n                                                                children: \"Industry Avg\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                                lineNumber: 466,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 464,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-4 h-4 text-secondary rotate-180\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 468,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-lg font-bold text-secondary\",\n                                                                children: [\n                                                                    _data_valueData__WEBPACK_IMPORTED_MODULE_2__.financialProjections.withZScore.badDebtRatio,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                                lineNumber: 470,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-text-muted\",\n                                                                children: \"With zScore\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                                lineNumber: 471,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 469,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-secondary\",\n                                        children: [\n                                            _data_valueData__WEBPACK_IMPORTED_MODULE_2__.financialProjections.improvements.badDebtReduction.toFixed(1),\n                                            \"% improvement\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 460,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium text-text-primary mb-2\",\n                                                children: \"User Churn Rate\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-lg font-bold text-red-500\",\n                                                                children: [\n                                                                    _data_valueData__WEBPACK_IMPORTED_MODULE_2__.financialProjections.industryAverages.userChurnRate,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                                lineNumber: 485,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-text-muted\",\n                                                                children: \"Industry Avg\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                                lineNumber: 486,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 484,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-4 h-4 text-secondary rotate-180\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 488,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-lg font-bold text-secondary\",\n                                                                children: [\n                                                                    _data_valueData__WEBPACK_IMPORTED_MODULE_2__.financialProjections.withZScore.userChurnRate,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                                lineNumber: 490,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-text-muted\",\n                                                                children: \"With zScore\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                                lineNumber: 491,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 489,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 483,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 481,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-secondary\",\n                                        children: [\n                                            _data_valueData__WEBPACK_IMPORTED_MODULE_2__.financialProjections.improvements.userRetentionIncrease.toFixed(1),\n                                            \"% improvement\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 480,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium text-text-primary mb-2\",\n                                                children: \"Operational Costs\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 502,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-lg font-bold text-red-500\",\n                                                                children: [\n                                                                    _data_valueData__WEBPACK_IMPORTED_MODULE_2__.financialProjections.industryAverages.operationalCosts,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                                lineNumber: 505,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-text-muted\",\n                                                                children: \"Industry Avg\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                                lineNumber: 506,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 504,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-4 h-4 text-secondary rotate-180\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-lg font-bold text-secondary\",\n                                                                children: [\n                                                                    _data_valueData__WEBPACK_IMPORTED_MODULE_2__.financialProjections.withZScore.operationalCosts,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                                lineNumber: 510,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-text-muted\",\n                                                                children: \"With zScore\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                                lineNumber: 511,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 509,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 503,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-secondary\",\n                                        children: [\n                                            _data_valueData__WEBPACK_IMPORTED_MODULE_2__.financialProjections.improvements.costReduction.toFixed(1),\n                                            \"% improvement\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 500,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 439,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                lineNumber: 434,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-primary to-secondary rounded-xl p-6 text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold mb-2\",\n                            children: \"Ready to Transform Your Lending Protocol?\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                            lineNumber: 525,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/90 mb-6 max-w-2xl mx-auto\",\n                            children: \"Based on 17 months of proven results: 37% liquidation reduction, 36% bad debt reduction, and $4.5M annual profit lift. Join the protocols already benefiting from zScore.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                            lineNumber: 526,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"px-6 py-3 bg-white text-primary font-semibold rounded-lg hover:bg-white/90 transition-colors\",\n                                    children: \"Schedule Demo\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                    lineNumber: 531,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"px-6 py-3 border border-white/30 text-white font-semibold rounded-lg hover:bg-white/10 transition-colors\",\n                                    children: \"Download Full Report\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                    lineNumber: 534,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                            lineNumber: 530,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                    lineNumber: 524,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                lineNumber: 523,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, this);\n}\n_s(LendingValue, \"FK+lanythtOz4SaKGTbw36M80b4=\");\n_c = LendingValue;\nvar _c;\n$RefreshReg$(_c, \"LendingValue\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/lending/Value.tsx\n"));

/***/ })

});