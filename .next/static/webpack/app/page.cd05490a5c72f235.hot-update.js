"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/lending/Value.tsx":
/*!******************************************!*\
  !*** ./src/components/lending/Value.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LendingValue; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,Award,BarChart3,Calculator,CheckCircle,Clock,DollarSign,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,Award,BarChart3,Calculator,CheckCircle,Clock,DollarSign,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,Award,BarChart3,Calculator,CheckCircle,Clock,DollarSign,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,Award,BarChart3,Calculator,CheckCircle,Clock,DollarSign,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,Award,BarChart3,Calculator,CheckCircle,Clock,DollarSign,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calculator.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,Award,BarChart3,Calculator,CheckCircle,Clock,DollarSign,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,Award,BarChart3,Calculator,CheckCircle,Clock,DollarSign,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,Award,BarChart3,Calculator,CheckCircle,Clock,DollarSign,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,Award,BarChart3,Calculator,CheckCircle,Clock,DollarSign,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,Award,BarChart3,Calculator,CheckCircle,Clock,DollarSign,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,Award,BarChart3,Calculator,CheckCircle,Clock,DollarSign,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,Award,BarChart3,Calculator,CheckCircle,Clock,DollarSign,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _data_valueData__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/valueData */ \"(app-pages-browser)/./src/data/valueData.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction LendingValue() {\n    _s();\n    const [selectedTimeframe, setSelectedTimeframe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"12-months\");\n    const [protocolSize, setProtocolSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"medium\");\n    const currentMetrics = _data_valueData__WEBPACK_IMPORTED_MODULE_2__.roiMetrics[selectedTimeframe];\n    const currentSize = _data_valueData__WEBPACK_IMPORTED_MODULE_2__.protocolSizes[protocolSize];\n    // Calculate ROI based on protocol size\n    const calculateROI = (baseValue)=>{\n        return (baseValue * currentSize.multiplier).toFixed(1);\n    };\n    const businessImpacts = [\n        {\n            title: \"Liquidation Risk Reduction\",\n            value: \"\".concat(currentMetrics.liquidationReduction, \"%\"),\n            description: \"Decrease in unexpected liquidations\",\n            icon: _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            color: \"text-green-500\",\n            bgColor: \"bg-green-50\",\n            trend: \"+15% vs industry average\"\n        },\n        {\n            title: \"Bad Debt Mitigation\",\n            value: \"\".concat(currentMetrics.badDebtReduction, \"%\"),\n            description: \"Reduction in protocol bad debt\",\n            icon: _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            color: \"text-blue-500\",\n            bgColor: \"bg-blue-50\",\n            trend: \"Industry leading performance\"\n        },\n        {\n            title: \"Revenue Growth\",\n            value: \"\".concat(currentMetrics.revenueIncrease, \"%\"),\n            description: \"Increase in protocol revenue\",\n            icon: _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            color: \"text-purple-500\",\n            bgColor: \"bg-purple-50\",\n            trend: \"$\".concat(calculateROI(currentMetrics.costSavings), \"M additional revenue\")\n        },\n        {\n            title: \"User Retention\",\n            value: \"\".concat(currentMetrics.userRetention, \"%\"),\n            description: \"Improvement in user retention\",\n            icon: _barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            color: \"text-orange-500\",\n            bgColor: \"bg-orange-50\",\n            trend: \"Higher lifetime value\"\n        }\n    ];\n    const implementationPhases = [\n        {\n            phase: \"Phase 1: Integration\",\n            duration: \"2-3 months\",\n            investment: \"$150K\",\n            roi: \"Break-even in 4 months\",\n            status: \"immediate\",\n            benefits: [\n                \"Basic zScore integration\",\n                \"Risk categorization\",\n                \"Initial liquidation reduction\"\n            ]\n        },\n        {\n            phase: \"Phase 2: Optimization\",\n            duration: \"3-4 months\",\n            investment: \"$200K\",\n            roi: \"300% ROI by month 8\",\n            status: \"short-term\",\n            benefits: [\n                \"Advanced risk models\",\n                \"Dynamic pricing\",\n                \"Cross-chain reputation\"\n            ]\n        },\n        {\n            phase: \"Phase 3: Advanced Features\",\n            duration: \"2-3 months\",\n            investment: \"$100K\",\n            roi: \"500% ROI by month 12\",\n            status: \"long-term\",\n            benefits: [\n                \"AI-powered predictions\",\n                \"Automated recommendations\",\n                \"Governance integration\"\n            ]\n        }\n    ];\n    const competitiveAdvantages = [\n        {\n            feature: \"Real-time Risk Assessment\",\n            traditional: \"Manual review, 24-48h delay\",\n            withZScore: \"Instant automated assessment\",\n            improvement: \"95% faster processing\"\n        },\n        {\n            feature: \"Liquidation Prediction\",\n            traditional: \"Reactive, post-event analysis\",\n            withZScore: \"Proactive, 24-48h advance warning\",\n            improvement: \"42% reduction in liquidations\"\n        },\n        {\n            feature: \"Cross-chain Reputation\",\n            traditional: \"Siloed, single-chain data\",\n            withZScore: \"Unified, multi-chain reputation\",\n            improvement: \"65% better risk assessment\"\n        },\n        {\n            feature: \"User Experience\",\n            traditional: \"Complex, manual processes\",\n            withZScore: \"Seamless, automated onboarding\",\n            improvement: \"35% higher user retention\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-4 sm:p-6 space-y-4 sm:space-y-6 bg-background min-h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface border border-border-color rounded-xl p-4 sm:p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col lg:flex-row lg:items-start lg:justify-between space-y-4 lg:space-y-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl sm:text-2xl font-bold text-text-primary mb-2\",\n                                    children: \"zScore ROI Impact Report\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-text-secondary mb-4 text-sm sm:text-base\",\n                                    children: \"Comprehensive analysis of financial benefits and business impact from implementing Zeru Finance zScore reputation system in DeFi lending protocols.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-3 py-1 bg-primary/10 text-primary rounded-full text-xs font-medium\",\n                                            children: \"Jan 2024 - May 2025\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-3 py-1 bg-secondary/10 text-secondary rounded-full text-xs font-medium\",\n                                            children: \"Live Data Analysis\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-3 py-1 bg-accent/10 text-accent rounded-full text-xs font-medium\",\n                                            children: \"Proven ROI\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-primary/10 to-secondary/10 border border-primary/20 rounded-lg p-4 lg:ml-4 flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-primary mb-1\",\n                                        children: \"500%+\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-text-secondary\",\n                                        children: \"Average ROI\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-text-muted mt-1\",\n                                        children: \"Within 12 months\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                lineNumber: 149,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface border border-border-color rounded-xl p-4 sm:p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-text-primary mb-4 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-5 h-5 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 11\n                            }, this),\n                            \"ROI Calculator\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-text-primary mb-2\",\n                                        children: \"Timeframe\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: selectedTimeframe,\n                                        onChange: (e)=>setSelectedTimeframe(e.target.value),\n                                        className: \"w-full p-3 border border-border-color rounded-lg bg-surface-light focus:ring-2 focus:ring-primary focus:border-transparent\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"12-months\",\n                                                children: \"12 Months\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"24-months\",\n                                                children: \"24 Months\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-text-primary mb-2\",\n                                        children: \"Protocol Size\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: protocolSize,\n                                        onChange: (e)=>setProtocolSize(e.target.value),\n                                        className: \"w-full p-3 border border-border-color rounded-lg bg-surface-light focus:ring-2 focus:ring-primary focus:border-transparent\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"small\",\n                                                children: \"Small ($50M TVL, 5K users)\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"medium\",\n                                                children: \"Medium ($500M TVL, 25K users)\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"large\",\n                                                children: \"Large ($2B+ TVL, 100K+ users)\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                lineNumber: 182,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6\",\n                children: businessImpacts.map((impact, index)=>{\n                    const Icon = impact.icon;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-surface border border-border-color rounded-xl p-4 sm:p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 \".concat(impact.bgColor, \" rounded-lg flex items-center justify-center\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: \"w-6 h-6 \".concat(impact.color)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-4 h-4 text-secondary\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-semibold text-text-primary mb-2\",\n                                children: impact.title\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-text-primary mb-1\",\n                                children: impact.value\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-text-secondary mb-2\",\n                                children: impact.description\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-secondary font-medium\",\n                                children: impact.trend\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                lineNumber: 219,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface border border-border-color rounded-xl p-4 sm:p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-text-primary mb-6 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"w-5 h-5 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 11\n                            }, this),\n                            \"Implementation Timeline & Expected ROI\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                        children: implementationPhases.map((phase, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-surface-light rounded-lg p-4 border border-border-color\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-text-primary\",\n                                                children: phase.phase\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(phase.status === \"immediate\" ? \"bg-green-100 text-green-700\" : phase.status === \"short-term\" ? \"bg-blue-100 text-blue-700\" : \"bg-purple-100 text-purple-700\"),\n                                                children: phase.status\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-text-secondary\",\n                                                        children: \"Duration:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-text-primary\",\n                                                        children: phase.duration\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-text-secondary\",\n                                                        children: \"Investment:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-text-primary\",\n                                                        children: phase.investment\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-text-secondary\",\n                                                        children: \"Expected ROI:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-secondary\",\n                                                        children: phase.roi\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-medium text-text-primary\",\n                                                children: \"Key Benefits:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 17\n                                            }, this),\n                                            phase.benefits.map((benefit, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"w-3 h-3 text-secondary flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                            lineNumber: 276,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-text-secondary\",\n                                                            children: benefit\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, idx, true, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                lineNumber: 240,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface border border-border-color rounded-xl p-4 sm:p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-text-primary mb-6 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"w-5 h-5 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 11\n                            }, this),\n                            \"Competitive Advantage Analysis\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        className: \"border-b border-border-color\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 font-semibold text-text-primary\",\n                                                children: \"Feature\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 font-semibold text-text-primary\",\n                                                children: \"Traditional Approach\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 font-semibold text-text-primary\",\n                                                children: \"With zScore\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 font-semibold text-text-primary\",\n                                                children: \"Improvement\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: competitiveAdvantages.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"border-b border-border-color/50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-4 px-4 font-medium text-text-primary\",\n                                                    children: item.feature\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-4 px-4 text-text-secondary\",\n                                                    children: item.traditional\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-4 px-4 text-text-secondary\",\n                                                    children: item.withZScore\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-4 px-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 bg-secondary/10 text-secondary rounded-full text-sm font-medium\",\n                                                        children: item.improvement\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                lineNumber: 287,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-primary/5 to-secondary/5 border border-primary/20 rounded-xl p-4 sm:p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-text-primary mb-6 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"w-5 h-5 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 11\n                            }, this),\n                            \"Financial Impact Summary (\",\n                            selectedTimeframe,\n                            \")\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/50 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-text-primary mb-2\",\n                                        children: \"Cost Savings\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-primary mb-1\",\n                                        children: [\n                                            \"$\",\n                                            calculateROI(currentMetrics.costSavings),\n                                            \"M\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-text-secondary\",\n                                        children: \"Reduced operational costs and bad debt\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/50 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-text-primary mb-2\",\n                                        children: \"Revenue Increase\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-secondary mb-1\",\n                                        children: [\n                                            \"+\",\n                                            currentMetrics.revenueIncrease,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-text-secondary\",\n                                        children: \"Higher user retention and volume\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/50 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-text-primary mb-2\",\n                                        children: \"Total ROI\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-accent mb-1\",\n                                        children: selectedTimeframe === \"12-months\" ? \"500%\" : \"750%\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-text-secondary\",\n                                        children: \"Return on investment\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                lineNumber: 321,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface border border-border-color rounded-xl p-4 sm:p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-text-primary mb-6 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"w-5 h-5 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 11\n                            }, this),\n                            \"Success Stories & Case Studies\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-surface-light rounded-lg p-4 border border-border-color\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-6 h-6 text-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 366,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-text-primary\",\n                                                        children: \"DeFi Protocol Alpha\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-text-secondary\",\n                                                        children: \"$1.2B TVL, 50K+ users\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 369,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-text-secondary\",\n                                                        children: \"Liquidation Reduction:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-secondary\",\n                                                        children: \"-45%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-text-secondary\",\n                                                        children: \"Bad Debt Reduction:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-secondary\",\n                                                        children: \"-62%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-text-secondary\",\n                                                        children: \"Revenue Increase:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-secondary\",\n                                                        children: \"+32%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-text-secondary\",\n                                                        children: \"Implementation Time:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-text-primary\",\n                                                        children: \"8 months\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 p-3 bg-primary/5 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-text-secondary italic\",\n                                                children: '\"zScore implementation reduced our liquidation events by 45% and significantly improved our risk assessment capabilities. The ROI was evident within 6 months.\"'\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-text-muted mt-2\",\n                                                children: \"- CTO, DeFi Protocol Alpha\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-surface-light rounded-lg p-4 border border-border-color\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-secondary/20 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_Award_BarChart3_Calculator_CheckCircle_Clock_DollarSign_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"w-6 h-6 text-secondary\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-text-primary\",\n                                                        children: \"Lending Platform Beta\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 407,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-text-secondary\",\n                                                        children: \"$800M TVL, 35K+ users\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 408,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 402,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-text-secondary\",\n                                                        children: \"User Retention:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-secondary\",\n                                                        children: \"+38%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 414,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 412,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-text-secondary\",\n                                                        children: \"Processing Speed:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-secondary\",\n                                                        children: \"95% faster\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 418,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-text-secondary\",\n                                                        children: \"Cost Reduction:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-secondary\",\n                                                        children: \"-55%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 422,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-text-secondary\",\n                                                        children: \"ROI Achievement:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-text-primary\",\n                                                        children: \"10 months\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                        lineNumber: 426,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 p-3 bg-secondary/5 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-text-secondary italic\",\n                                                children: '\"The automated risk assessment and cross-chain reputation tracking transformed our lending operations. User experience improved dramatically.\"'\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-text-muted mt-2\",\n                                                children: \"- Head of Risk, Lending Platform Beta\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                        lineNumber: 363,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                lineNumber: 358,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-primary to-secondary rounded-xl p-6 text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold mb-2\",\n                            children: \"Ready to Transform Your Lending Protocol?\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                            lineNumber: 443,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/90 mb-6 max-w-2xl mx-auto\",\n                            children: \"Join leading DeFi protocols that have already implemented zScore and achieved significant ROI improvements. Start your journey to better risk management today.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                            lineNumber: 444,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"px-6 py-3 bg-white text-primary font-semibold rounded-lg hover:bg-white/90 transition-colors\",\n                                    children: \"Schedule Demo\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                    lineNumber: 449,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"px-6 py-3 border border-white/30 text-white font-semibold rounded-lg hover:bg-white/10 transition-colors\",\n                                    children: \"Download Full Report\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                                    lineNumber: 452,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                            lineNumber: 448,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                    lineNumber: 442,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n                lineNumber: 441,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Zeru/stontiges/apps/usecasedashboard/src/components/lending/Value.tsx\",\n        lineNumber: 147,\n        columnNumber: 5\n    }, this);\n}\n_s(LendingValue, \"thRYqejP5hfDHh7aLeKKXwezsMI=\");\n_c = LendingValue;\nvar _c;\n$RefreshReg$(_c, \"LendingValue\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/lending/Value.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/data/valueData.ts":
/*!*******************************!*\
  !*** ./src/data/valueData.ts ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   caseStudies: function() { return /* binding */ caseStudies; },\n/* harmony export */   competitiveAdvantages: function() { return /* binding */ competitiveAdvantages; },\n/* harmony export */   financialProjections: function() { return /* binding */ financialProjections; },\n/* harmony export */   implementationPhases: function() { return /* binding */ implementationPhases; },\n/* harmony export */   marketOpportunity: function() { return /* binding */ marketOpportunity; },\n/* harmony export */   protocolSizes: function() { return /* binding */ protocolSizes; },\n/* harmony export */   riskMitigationBenefits: function() { return /* binding */ riskMitigationBenefits; },\n/* harmony export */   roiMetrics: function() { return /* binding */ roiMetrics; },\n/* harmony export */   technologyAdvantages: function() { return /* binding */ technologyAdvantages; }\n/* harmony export */ });\n// ROI and Value proposition data for zScore implementation\n// ROI metrics by timeframe\nconst roiMetrics = {\n    \"12-months\": {\n        liquidationReduction: 42,\n        badDebtReduction: 65,\n        revenueIncrease: 28,\n        costSavings: 1.2,\n        userRetention: 35,\n        operationalEfficiency: 45\n    },\n    \"24-months\": {\n        liquidationReduction: 58,\n        badDebtReduction: 78,\n        revenueIncrease: 45,\n        costSavings: 2.8,\n        userRetention: 52,\n        operationalEfficiency: 67\n    }\n};\n// Protocol size configurations\nconst protocolSizes = {\n    small: {\n        tvl: \"50M\",\n        users: \"5K\",\n        multiplier: 0.5\n    },\n    medium: {\n        tvl: \"500M\",\n        users: \"25K\",\n        multiplier: 1.0\n    },\n    large: {\n        tvl: \"2B\",\n        users: \"100K\",\n        multiplier: 2.5\n    }\n};\n// Implementation phases with detailed ROI projections\nconst implementationPhases = [\n    {\n        phase: \"Phase 1: Integration\",\n        duration: \"2-3 months\",\n        investment: \"$150K\",\n        roi: \"Break-even in 4 months\",\n        status: \"immediate\",\n        benefits: [\n            \"Basic zScore integration\",\n            \"Risk categorization\",\n            \"Initial liquidation reduction\",\n            \"Automated risk scoring\"\n        ]\n    },\n    {\n        phase: \"Phase 2: Optimization\",\n        duration: \"3-4 months\",\n        investment: \"$200K\",\n        roi: \"300% ROI by month 8\",\n        status: \"short-term\",\n        benefits: [\n            \"Advanced risk models\",\n            \"Dynamic pricing\",\n            \"Cross-chain reputation\",\n            \"Real-time monitoring\"\n        ]\n    },\n    {\n        phase: \"Phase 3: Advanced Features\",\n        duration: \"2-3 months\",\n        investment: \"$100K\",\n        roi: \"500% ROI by month 12\",\n        status: \"long-term\",\n        benefits: [\n            \"AI-powered predictions\",\n            \"Automated recommendations\",\n            \"Governance integration\",\n            \"Advanced analytics\"\n        ]\n    }\n];\n// Competitive advantages analysis\nconst competitiveAdvantages = [\n    {\n        feature: \"Real-time Risk Assessment\",\n        traditional: \"Manual review, 24-48h delay\",\n        withZScore: \"Instant automated assessment\",\n        improvement: \"95% faster processing\"\n    },\n    {\n        feature: \"Liquidation Prediction\",\n        traditional: \"Reactive, post-event analysis\",\n        withZScore: \"Proactive, 24-48h advance warning\",\n        improvement: \"42% reduction in liquidations\"\n    },\n    {\n        feature: \"Cross-chain Reputation\",\n        traditional: \"Siloed, single-chain data\",\n        withZScore: \"Unified, multi-chain reputation\",\n        improvement: \"65% better risk assessment\"\n    },\n    {\n        feature: \"User Experience\",\n        traditional: \"Complex, manual processes\",\n        withZScore: \"Seamless, automated onboarding\",\n        improvement: \"35% higher user retention\"\n    },\n    {\n        feature: \"Bad Debt Management\",\n        traditional: \"Reactive liquidations\",\n        withZScore: \"Predictive risk mitigation\",\n        improvement: \"65% reduction in bad debt\"\n    },\n    {\n        feature: \"Operational Costs\",\n        traditional: \"High manual oversight\",\n        withZScore: \"Automated risk management\",\n        improvement: \"55% cost reduction\"\n    }\n];\n// Success stories and case studies\nconst caseStudies = [\n    {\n        name: \"DeFi Protocol Alpha\",\n        tvl: \"$1.2B\",\n        users: \"50K+\",\n        liquidationReduction: 45,\n        badDebtReduction: 62,\n        revenueIncrease: 32,\n        implementationTime: \"8 months\",\n        testimonial: \"zScore implementation reduced our liquidation events by 45% and significantly improved our risk assessment capabilities. The ROI was evident within 6 months.\",\n        author: \"CTO, DeFi Protocol Alpha\",\n        icon: \"zap\"\n    },\n    {\n        name: \"Lending Platform Beta\",\n        tvl: \"$800M\",\n        users: \"35K+\",\n        liquidationReduction: 38,\n        badDebtReduction: 55,\n        revenueIncrease: 28,\n        implementationTime: \"10 months\",\n        testimonial: \"The automated risk assessment and cross-chain reputation tracking transformed our lending operations. User experience improved dramatically.\",\n        author: \"Head of Risk, Lending Platform Beta\",\n        icon: \"shield\"\n    },\n    {\n        name: \"Multi-Chain Lender Gamma\",\n        tvl: \"$2.1B\",\n        users: \"75K+\",\n        liquidationReduction: 52,\n        badDebtReduction: 71,\n        revenueIncrease: 41,\n        implementationTime: \"6 months\",\n        testimonial: \"Cross-chain reputation tracking was a game-changer. We saw immediate improvements in risk assessment accuracy and user trust.\",\n        author: \"CEO, Multi-Chain Lender Gamma\",\n        icon: \"target\"\n    }\n];\n// Key financial metrics and projections\nconst financialProjections = {\n    industryAverages: {\n        liquidationRate: 8.5,\n        badDebtRatio: 4.2,\n        userChurnRate: 25,\n        operationalCosts: 15\n    },\n    withZScore: {\n        liquidationRate: 4.9,\n        badDebtRatio: 1.5,\n        userChurnRate: 16,\n        operationalCosts: 6.8\n    },\n    improvements: {\n        liquidationReduction: 42.4,\n        badDebtReduction: 64.3,\n        userRetentionIncrease: 36.0,\n        costReduction: 54.7\n    }\n};\n// Market opportunity and addressable market\nconst marketOpportunity = {\n    totalAddressableMarket: \"$18.7B\",\n    servicableAddressableMarket: \"$8.2B\",\n    servicableObtainableMarket: \"$1.4B\",\n    marketGrowthRate: 23.5,\n    protocolsAddressed: 150,\n    potentialRevenue: \"$420M\"\n};\n// Risk mitigation benefits\nconst riskMitigationBenefits = [\n    {\n        risk: \"Liquidation Cascade Events\",\n        impact: \"High\",\n        probability: \"Medium\",\n        mitigation: \"Real-time risk monitoring and early warning systems\",\n        reduction: \"65%\"\n    },\n    {\n        risk: \"Bad Debt Accumulation\",\n        impact: \"High\",\n        probability: \"High\",\n        mitigation: \"Predictive modeling and proactive interventions\",\n        reduction: \"72%\"\n    },\n    {\n        risk: \"Regulatory Compliance\",\n        impact: \"Medium\",\n        probability: \"Low\",\n        mitigation: \"Transparent and auditable risk assessment\",\n        reduction: \"85%\"\n    },\n    {\n        risk: \"User Trust Issues\",\n        impact: \"Medium\",\n        probability: \"Medium\",\n        mitigation: \"Transparent reputation scoring and explanations\",\n        reduction: \"45%\"\n    }\n];\n// Technology advantages\nconst technologyAdvantages = [\n    {\n        feature: \"EigenLayer Integration\",\n        benefit: \"Decentralized and secure reputation system\",\n        impact: \"Enhanced trust and security\"\n    },\n    {\n        feature: \"Multi-chain Support\",\n        benefit: \"Unified reputation across all major chains\",\n        impact: \"Comprehensive risk assessment\"\n    },\n    {\n        feature: \"Real-time Processing\",\n        benefit: \"Instant risk score updates\",\n        impact: \"Immediate risk mitigation\"\n    },\n    {\n        feature: \"AI-Powered Analytics\",\n        benefit: \"Advanced pattern recognition\",\n        impact: \"Predictive risk management\"\n    },\n    {\n        feature: \"Open Source\",\n        benefit: \"Transparent and auditable algorithms\",\n        impact: \"Community trust and adoption\"\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/data/valueData.ts\n"));

/***/ })

});