'use client'

import { useState, useEffect } from 'react'
import Sidebar from '@/components/Sidebar'
import TabSystem from '@/components/TabSystem'
import Header from '@/components/Header'

export default function Home() {
  const [selectedSector, setSelectedSector] = useState('lending')
  const [selectedTab, setSelectedTab] = useState('real-usecase')
  const [sidebarOpen, setSidebarOpen] = useState(false)

  // Redirect from LLM tab to real-usecase since LLM is now integrated into real-usecase
  useEffect(() => {
    if (selectedTab === 'llm') {
      setSelectedTab('real-usecase')
    }
  }, [selectedTab])

  return (
    <div className="flex h-screen bg-background">
      {/* Mobile Sidebar Overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={`${
        sidebarOpen ? 'translate-x-0' : '-translate-x-full'
      } fixed inset-y-0 left-0 z-50 lg:relative lg:translate-x-0 lg:z-auto transition-transform duration-300 ease-in-out lg:transition-none`}>
        <Sidebar
          selectedSector={selectedSector}
          onSectorChange={setSelectedSector}
          onClose={() => setSidebarOpen(false)}
        />
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <Header onMenuClick={() => setSidebarOpen(true)} />

        {/* Tab System and Content */}
        <div className="flex-1 overflow-hidden">
          <TabSystem
            selectedSector={selectedSector}
            selectedTab={selectedTab}
            onTabChange={setSelectedTab}
          />
        </div>
      </div>
    </div>
  )
}
