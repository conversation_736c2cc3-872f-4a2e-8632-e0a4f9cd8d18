'use client'

import {
  <PERSON>,
  Tren<PERSON>Down,
  <PERSON><PERSON><PERSON><PERSON>gle,
  <PERSON><PERSON>ign,
  Bar<PERSON>hart3,
  Shield
} from 'lucide-react'
import MetricCard from '@/components/charts/MetricCard'
import RiskDistributionChart from '@/components/charts/RiskDistributionChart'
import ChainDistributionChart from '@/components/charts/ChainDistributionChart'
import LiquidationTrendsChart from '@/components/charts/LiquidationTrendsChart'
import TopWalletsTable from '@/components/charts/TopWalletsTable'
import LLMSidebar from './LLMSidebar'
import { keyFindings } from '@/data/liquidationData'

export default function LendingRealUsecase() {
  return (
    <div className="p-4 sm:p-6 bg-background min-h-full">
      <div className="flex flex-col xl:flex-row gap-4 sm:gap-6">
        {/* Main Content */}
        <div className="flex-1 space-y-4 sm:space-y-6">
      {/* Header Section */}
      <div className="bg-surface border border-border-color rounded-xl p-4 sm:p-6">
        <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between space-y-4 lg:space-y-0">
          <div className="flex-1">
            <h1 className="text-xl sm:text-2xl font-bold text-text-primary mb-2">
              DeFi Lending Creditworthiness Analysis
            </h1>
            <p className="text-text-secondary mb-4 text-sm sm:text-base">
              Comprehensive analysis of DeFi wallet creditworthiness using zScore metrics,
              based on a study of {keyFindings.totalLiquidatedAddresses.toLocaleString()} liquidated wallet addresses
              on Aave over the last 48 hours (02-02-2025 to 03-02-2025).
            </p>
            <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-4 space-y-2 sm:space-y-0 text-sm">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-secondary rounded-full" />
                <span className="text-text-secondary">Live Data</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-primary rounded-full" />
                <span className="text-text-secondary">Real-time Analysis</span>
              </div>
            </div>
          </div>
          <div className="bg-gradient-to-r from-primary/10 to-primary-light/10 border border-primary/20 rounded-lg p-4 lg:ml-4 flex-shrink-0">
            <div className="text-center">
              <Shield className="w-8 h-8 text-primary mx-auto mb-2" />
              <p className="text-sm font-medium text-text-primary">Coverage</p>
              <p className="text-2xl font-bold text-primary">{keyFindings.coveragePercentage}%</p>
              <p className="text-xs text-text-secondary">
                {keyFindings.analyzedWallets.toLocaleString()} wallets analyzed
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
        <MetricCard
          title="Total Wallets Tracked"
          value={keyFindings.totalLiquidatedAddresses.toLocaleString()}
          change="+2.4% this week"
          changeType="positive"
          icon={Users}
          description="Liquidated addresses analyzed"
        />
        <MetricCard
          title="Chains Integrated"
          value="10"
          change="+2 new this month"
          changeType="positive"
          icon={BarChart3}
          description="Blockchain networks covered"
        />
        <MetricCard
          title="Protocols Supported"
          value="25+"
          change="Aave, Compound, MakerDAO"
          changeType="neutral"
          icon={DollarSign}
          description="DeFi lending protocols"
        />
        <MetricCard
          title="Daily Transactions"
          value="1.2M"
          change="+10.7% increase"
          changeType="positive"
          icon={TrendingDown}
          description="Real-time monitoring"
        />
      </div>

      {/* Research Overview */}
      <div className="bg-surface border border-border-color rounded-xl p-4 sm:p-6">
        <h2 className="text-xl font-semibold text-text-primary mb-4">Research Question</h2>
        <div className="bg-gradient-to-r from-primary/5 to-primary-light/5 border border-primary/10 rounded-lg p-4 sm:p-6">
          <h3 className="text-base sm:text-lg font-medium text-text-primary mb-3">
            Can a decentralized reputation system built on Eigenlayer be a good alibi for creditworthiness?
          </h3>
          <p className="text-text-secondary leading-relaxed text-sm sm:text-base">
            This research presents a comprehensive analysis of DeFi wallet creditworthiness using zScore metrics.
            The findings demonstrate how zScore can serve as a reliable predictor for liquidation risk assessment
            in DeFi lending, with the primary risk zone (190-200) accounting for <strong className="text-primary">28.87%</strong> of
            all liquidations.
          </p>
        </div>
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
        <RiskDistributionChart />
        <ChainDistributionChart />
      </div>

      <div className="grid grid-cols-1 gap-6">
        <LiquidationTrendsChart />
      </div>

      {/* Key Findings */}
      <div className="bg-surface border border-border-color rounded-xl p-4 sm:p-6">
        <h2 className="text-xl font-semibold text-text-primary mb-6">Key Findings</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
          <div className="bg-surface-light rounded-lg p-4">
            <div className="flex items-center space-x-3 mb-3">
              <AlertTriangle className="w-6 h-6 text-danger" />
              <h3 className="font-medium text-text-primary">Primary Risk Zone</h3>
            </div>
            <p className="text-2xl font-bold text-danger mb-2">190-200</p>
            <p className="text-sm text-text-secondary">
              {keyFindings.primaryRiskZone.percentage}% of liquidations ({keyFindings.primaryRiskZone.count.toLocaleString()} wallets)
            </p>
          </div>

          <div className="bg-surface-light rounded-lg p-4">
            <div className="flex items-center space-x-3 mb-3">
              <BarChart3 className="w-6 h-6 text-primary" />
              <h3 className="font-medium text-text-primary">Average zScore</h3>
            </div>
            <p className="text-2xl font-bold text-primary mb-2">{keyFindings.averageZScore}</p>
            <p className="text-sm text-text-secondary">
              Median: {keyFindings.medianZScore} (right-skewed distribution)
            </p>
          </div>

          <div className="bg-surface-light rounded-lg p-4">
            <div className="flex items-center space-x-3 mb-3">
              <TrendingDown className="w-6 h-6 text-accent" />
              <h3 className="font-medium text-text-primary">High Risk Threshold</h3>
            </div>
            <p className="text-2xl font-bold text-accent mb-2">&lt;200</p>
            <p className="text-sm text-text-secondary">
              ~45% of liquidations occur in this range
            </p>
          </div>
        </div>
      </div>

      {/* Top Wallets Table */}
      <TopWalletsTable />

      {/* Risk Assessment Framework */}
      <div className="bg-surface border border-border-color rounded-xl p-4 sm:p-6">
        <h2 className="text-xl font-semibold text-text-primary mb-6">Risk Assessment Framework</h2>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-border-color">
                <th className="text-left py-3 px-4 text-sm font-medium text-text-secondary">Risk Level</th>
                <th className="text-left py-3 px-4 text-sm font-medium text-text-secondary">Score Range</th>
                <th className="text-left py-3 px-4 text-sm font-medium text-text-secondary">% of Liquidations</th>
                <th className="text-left py-3 px-4 text-sm font-medium text-text-secondary">Implication</th>
              </tr>
            </thead>
            <tbody>
              <tr className="border-b border-border-color/50">
                <td className="py-3 px-4">
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-danger/10 text-danger border border-danger/20">
                    Very High
                  </span>
                </td>
                <td className="py-3 px-4 text-text-primary">&lt;200</td>
                <td className="py-3 px-4 text-text-primary">~45%</td>
                <td className="py-3 px-4 text-text-secondary">Needs max collateralization</td>
              </tr>
              <tr className="border-b border-border-color/50">
                <td className="py-3 px-4">
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-accent/10 text-accent border border-accent/20">
                    High
                  </span>
                </td>
                <td className="py-3 px-4 text-text-primary">200–300</td>
                <td className="py-3 px-4 text-text-primary">~30%</td>
                <td className="py-3 px-4 text-text-secondary">Higher liquidation probability</td>
              </tr>
              <tr className="border-b border-border-color/50">
                <td className="py-3 px-4">
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-secondary/10 text-secondary border border-secondary/20">
                    Moderate
                  </span>
                </td>
                <td className="py-3 px-4 text-text-primary">300–400</td>
                <td className="py-3 px-4 text-text-primary">~20%</td>
                <td className="py-3 px-4 text-text-secondary">Standard collateralization</td>
              </tr>
              <tr>
                <td className="py-3 px-4">
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary border border-primary/20">
                    Lower Risk
                  </span>
                </td>
                <td className="py-3 px-4 text-text-primary">400+</td>
                <td className="py-3 px-4 text-text-primary">~5%</td>
                <td className="py-3 px-4 text-text-secondary">Favorable lending terms</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
        </div>

        {/* LLM Sidebar */}
        <div className="w-full xl:w-80 xl:flex-shrink-0">
          <div className="xl:sticky xl:top-6">
            <LLMSidebar />
          </div>
        </div>
      </div>
    </div>
  )
}
