'use client'

import {
  DollarSign,
  TrendingUp,
  Shield,
  Target,
  Calculator,
  Award,
  BarChart3,
  Users,
  Clock,
  CheckCircle,
  ArrowUpRight,
  Zap
} from 'lucide-react'
import { useState } from 'react'
import {
  roiMetrics,
  protocolSizes,
  implementationPhases,
  competitiveAdvantages,
  caseStudies,
  financialProjections,
  marketOpportunity,
  riskMitigationBenefits
} from '@/data/valueData'

export default function LendingValue() {
  const [selectedTimeframe, setSelectedTimeframe] = useState('17-months-actual')
  const [protocolSize, setProtocolSize] = useState('medium')

  const currentMetrics = roiMetrics[selectedTimeframe as keyof typeof roiMetrics]
  const currentSize = protocolSizes[protocolSize as keyof typeof protocolSizes]

  // Calculate ROI based on protocol size
  const calculateROI = (baseValue: number) => {
    return (baseValue * currentSize.multiplier).toFixed(1)
  }

  const businessImpacts = [
    {
      title: "Liquidation Risk Reduction",
      value: `${currentMetrics.liquidationReduction}%`,
      description: "Decrease in unexpected liquidations",
      icon: Shield,
      color: "text-green-500",
      bgColor: "bg-green-50",
      trend: "+15% vs industry average"
    },
    {
      title: "Bad Debt Mitigation",
      value: `${currentMetrics.badDebtReduction}%`,
      description: "Reduction in protocol bad debt",
      icon: Target,
      color: "text-blue-500",
      bgColor: "bg-blue-50",
      trend: "Industry leading performance"
    },
    {
      title: "Revenue Growth",
      value: `${currentMetrics.revenueIncrease}%`,
      description: "Increase in protocol revenue",
      icon: TrendingUp,
      color: "text-purple-500",
      bgColor: "bg-purple-50",
      trend: `$${calculateROI(currentMetrics.costSavings)}M additional revenue`
    },
    {
      title: "User Retention",
      value: `${currentMetrics.userRetention}%`,
      description: "Improvement in user retention",
      icon: Users,
      color: "text-orange-500",
      bgColor: "bg-orange-50",
      trend: "Higher lifetime value"
    }
  ]

  return (
    <div className="p-4 sm:p-6 space-y-4 sm:space-y-6 bg-background min-h-full">
      {/* Header */}
      <div className="bg-surface border border-border-color rounded-xl p-4 sm:p-6">
        <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between space-y-4 lg:space-y-0">
          <div className="flex-1">
            <h1 className="text-xl sm:text-2xl font-bold text-text-primary mb-2">
              zScore ROI Impact Report
            </h1>
            <p className="text-text-secondary mb-4 text-sm sm:text-base">
              Comprehensive analysis of financial benefits and business impact from implementing
              Zeru Finance zScore reputation system in DeFi lending protocols.
            </p>
            <div className="flex flex-wrap gap-2">
              <span className="px-3 py-1 bg-primary/10 text-primary rounded-full text-xs font-medium">
                Jan 2024 - May 2025
              </span>
              <span className="px-3 py-1 bg-secondary/10 text-secondary rounded-full text-xs font-medium">
                Live Data Analysis
              </span>
              <span className="px-3 py-1 bg-accent/10 text-accent rounded-full text-xs font-medium">
                Proven ROI
              </span>
            </div>
          </div>
          <div className="bg-gradient-to-r from-primary/10 to-secondary/10 border border-primary/20 rounded-lg p-4 lg:ml-4 flex-shrink-0">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary mb-1">$4.5M</div>
              <div className="text-sm text-text-secondary">Annual Profit Lift</div>
              <div className="text-xs text-text-muted mt-1">Proven Results</div>
            </div>
          </div>
        </div>
      </div>

      {/* Proven Results Highlight */}
      <div className="bg-gradient-to-r from-green-50 to-blue-50 border-2 border-green-200 rounded-xl p-4 sm:p-6">
        <div className="flex items-center justify-center mb-4">
          <CheckCircle className="w-8 h-8 text-green-600 mr-3" />
          <h2 className="text-xl font-bold text-green-800">ACTUAL PROVEN RESULTS</h2>
        </div>
        <div className="text-center mb-4">
          <p className="text-green-700 font-medium mb-2">
            All metrics below are based on <strong>real data</strong> from the Zeru Finance zScore ROI Impact Report
          </p>
          <p className="text-green-600 text-sm">
            Analysis Period: <strong>January 2024 - May 2025</strong> |
            Sample Size: <strong>10,000+ borrowers</strong> |
            Statistical Significance: <strong>95%+ confidence</strong>
          </p>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div className="bg-white/70 rounded-lg p-3">
            <div className="text-2xl font-bold text-green-600">37%</div>
            <div className="text-sm text-green-700">Liquidation Reduction</div>
            <div className="text-xs text-green-600">10.2% → 6.4%</div>
          </div>
          <div className="bg-white/70 rounded-lg p-3">
            <div className="text-2xl font-bold text-green-600">36%</div>
            <div className="text-sm text-green-700">Bad Debt Reduction</div>
            <div className="text-xs text-green-600">0.50% → 0.32%</div>
          </div>
          <div className="bg-white/70 rounded-lg p-3">
            <div className="text-2xl font-bold text-green-600">$900</div>
            <div className="text-sm text-green-700">Revenue per User</div>
            <div className="text-xs text-green-600">+12% increase</div>
          </div>
          <div className="bg-white/70 rounded-lg p-3">
            <div className="text-2xl font-bold text-green-600">$200M</div>
            <div className="text-sm text-green-700">Unlocked Capacity</div>
            <div className="text-xs text-green-600">LTV: 67% → 80%</div>
          </div>
        </div>
      </div>

      {/* ROI Calculator Controls */}
      <div className="bg-surface border border-border-color rounded-xl p-4 sm:p-6">
        <h2 className="text-xl font-semibold text-text-primary mb-4 flex items-center">
          <Calculator className="w-5 h-5 mr-2" />
          ROI Calculator
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              Timeframe
            </label>
            <select
              value={selectedTimeframe}
              onChange={(e) => setSelectedTimeframe(e.target.value)}
              className="w-full p-3 border border-border-color rounded-lg bg-surface-light focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              <option value="12-months-projected">12 Months (Projected)</option>
              <option value="17-months-actual">17 Months (ACTUAL PROVEN DATA - Jan 2024 to May 2025)</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              Protocol Size
            </label>
            <select
              value={protocolSize}
              onChange={(e) => setProtocolSize(e.target.value)}
              className="w-full p-3 border border-border-color rounded-lg bg-surface-light focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              <option value="small">Small ($50M TVL, 5K users)</option>
              <option value="medium">Medium ($500M TVL, 25K users)</option>
              <option value="large">Large ($2B+ TVL, 100K+ users)</option>
            </select>
          </div>
        </div>
      </div>

      {/* Business Impact Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
        {businessImpacts.map((impact, index) => {
          const Icon = impact.icon
          return (
            <div key={index} className="bg-surface border border-border-color rounded-xl p-4 sm:p-6">
              <div className="flex items-center justify-between mb-4">
                <div className={`w-12 h-12 ${impact.bgColor} rounded-lg flex items-center justify-center`}>
                  <Icon className={`w-6 h-6 ${impact.color}`} />
                </div>
                <ArrowUpRight className="w-4 h-4 text-secondary" />
              </div>
              <h3 className="font-semibold text-text-primary mb-2">{impact.title}</h3>
              <div className="text-2xl font-bold text-text-primary mb-1">{impact.value}</div>
              <p className="text-sm text-text-secondary mb-2">{impact.description}</p>
              <div className="text-xs text-secondary font-medium">{impact.trend}</div>
            </div>
          )
        })}
      </div>

      {/* Competitive Advantage */}
      <div className="bg-surface border border-border-color rounded-xl p-4 sm:p-6">
        <h2 className="text-xl font-semibold text-text-primary mb-6 flex items-center">
          <Award className="w-5 h-5 mr-2" />
          Competitive Advantage Analysis
        </h2>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-border-color">
                <th className="text-left py-3 px-4 font-semibold text-text-primary">Feature</th>
                <th className="text-left py-3 px-4 font-semibold text-text-primary">Traditional Approach</th>
                <th className="text-left py-3 px-4 font-semibold text-text-primary">With zScore</th>
                <th className="text-left py-3 px-4 font-semibold text-text-primary">Improvement</th>
              </tr>
            </thead>
            <tbody>
              {competitiveAdvantages.map((item, index) => (
                <tr key={index} className="border-b border-border-color/50">
                  <td className="py-4 px-4 font-medium text-text-primary">{item.feature}</td>
                  <td className="py-4 px-4 text-text-secondary">{item.traditional}</td>
                  <td className="py-4 px-4 text-text-secondary">{item.withZScore}</td>
                  <td className="py-4 px-4">
                    <span className="px-2 py-1 bg-secondary/10 text-secondary rounded-full text-sm font-medium">
                      {item.improvement}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Waterfall Chart - Exact from ROI Report */}
      <div className="bg-gradient-to-r from-primary/5 to-secondary/5 border border-primary/20 rounded-xl p-4 sm:p-6">
        <h2 className="text-xl font-semibold text-text-primary mb-6 flex items-center">
          <DollarSign className="w-5 h-5 mr-2" />
          Lending $ Impact Waterfall (From ROI Report Figure 3)
        </h2>
        <div className="bg-white/70 rounded-lg p-6 mb-6">
          <h3 className="text-lg font-semibold text-text-primary mb-4 text-center">
            Net Annual Profit: $98M → $102.5M (+$4.5M)
          </h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200">
              <div>
                <div className="font-semibold text-green-800">1. Avoided Bad Debt Losses</div>
                <div className="text-sm text-green-600">Risk reduction from filtering low zScore borrowers</div>
              </div>
              <div className="text-xl font-bold text-green-600">+$1.5M</div>
            </div>

            <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-200">
              <div>
                <div className="font-semibold text-red-800">2. Reduced Risky-Loan Interest</div>
                <div className="text-sm text-red-600">Revenue lost from loans not granted to low zScores</div>
              </div>
              <div className="text-xl font-bold text-red-600">-$5.0M</div>
            </div>

            <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-200">
              <div>
                <div className="font-semibold text-blue-800">3. Additional Interest from High-zScore Borrowers</div>
                <div className="text-sm text-blue-600">Higher LTV (67% → 80%) and new loans to creditworthy users</div>
              </div>
              <div className="text-xl font-bold text-blue-600">+$8.0M</div>
            </div>

            <div className="border-t-2 border-gray-300 pt-3">
              <div className="flex items-center justify-between p-3 bg-primary/10 rounded-lg border border-primary/30">
                <div>
                  <div className="font-bold text-primary text-lg">NET RESULT</div>
                  <div className="text-sm text-text-secondary">Total annual profit improvement</div>
                </div>
                <div className="text-2xl font-bold text-primary">+$4.5M</div>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white/50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-primary mb-1">$200M</div>
            <div className="text-sm font-medium text-text-primary mb-1">Unlocked Capacity</div>
            <div className="text-xs text-text-secondary">Additional borrowing on Ethereum L1</div>
          </div>
          <div className="bg-white/50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-secondary mb-1">10,000+</div>
            <div className="text-sm font-medium text-text-primary mb-1">Borrowers Analyzed</div>
            <div className="text-xs text-text-secondary">Statistical significance p&lt;0.001</div>
          </div>
          <div className="bg-white/50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-accent mb-1">$1.5B</div>
            <div className="text-sm font-medium text-text-primary mb-1">Collateral Analyzed</div>
            <div className="text-xs text-text-secondary">Ethereum & L2 aggregate</div>
          </div>
        </div>
      </div>

      {/* Case Studies */}
      <div className="bg-surface border border-border-color rounded-xl p-4 sm:p-6">
        <h2 className="text-xl font-semibold text-text-primary mb-6 flex items-center">
          <BarChart3 className="w-5 h-5 mr-2" />
          Success Stories & Case Studies
        </h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {caseStudies.map((study, index) => {
            const IconComponent = study.icon === 'zap' ? Zap : study.icon === 'shield' ? Shield : Target
            const colorClass = index === 0 ? 'primary' : index === 1 ? 'secondary' : 'accent'

            return (
              <div key={index} className="bg-surface-light rounded-lg p-4 border border-border-color">
                <div className="flex items-center space-x-3 mb-4">
                  <div className={`w-12 h-12 bg-${colorClass}/20 rounded-lg flex items-center justify-center`}>
                    <IconComponent className={`w-6 h-6 text-${colorClass}`} />
                  </div>
                  <div>
                    <h3 className="font-semibold text-text-primary">{study.name}</h3>
                    <p className="text-sm text-text-secondary">{study.tvl} TVL, {study.users} users</p>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-text-secondary">Liquidation Reduction:</span>
                    <span className="text-sm font-medium text-secondary">-{study.liquidationReduction}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-text-secondary">Bad Debt Reduction:</span>
                    <span className="text-sm font-medium text-secondary">-{study.badDebtReduction}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-text-secondary">Revenue Increase:</span>
                    <span className="text-sm font-medium text-secondary">+{study.revenueIncrease}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-text-secondary">Implementation Time:</span>
                    <span className="text-sm font-medium text-text-primary">{study.implementationTime}</span>
                  </div>
                </div>
                <div className={`mt-4 p-3 bg-${colorClass}/5 rounded-lg`}>
                  <p className="text-sm text-text-secondary italic">
                    "{study.testimonial}"
                  </p>
                  <p className="text-xs text-text-muted mt-2">- {study.author}</p>
                </div>
              </div>
            )
          })}
        </div>
      </div>

      {/* Market Opportunity */}
      <div className="bg-surface border border-border-color rounded-xl p-4 sm:p-6">
        <h2 className="text-xl font-semibold text-text-primary mb-6 flex items-center">
          <Target className="w-5 h-5 mr-2" />
          Market Opportunity & Growth Potential
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="text-3xl font-bold text-primary mb-2">{marketOpportunity.totalAddressableMarket}</div>
            <div className="text-sm font-medium text-text-primary mb-1">Total Addressable Market</div>
            <div className="text-xs text-text-secondary">DeFi lending protocols globally</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-secondary mb-2">{marketOpportunity.marketGrowthRate}%</div>
            <div className="text-sm font-medium text-text-primary mb-1">Annual Growth Rate</div>
            <div className="text-xs text-text-secondary">Year-over-year expansion</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-accent mb-2">{marketOpportunity.protocolsAddressed}</div>
            <div className="text-sm font-medium text-text-primary mb-1">Protocols Addressable</div>
            <div className="text-xs text-text-secondary">Potential integration targets</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-primary mb-2">{marketOpportunity.potentialRevenue}</div>
            <div className="text-sm font-medium text-text-primary mb-1">Revenue Potential</div>
            <div className="text-xs text-text-secondary">5-year projection</div>
          </div>
        </div>
      </div>

      {/* Risk Mitigation Benefits */}
      <div className="bg-surface border border-border-color rounded-xl p-4 sm:p-6">
        <h2 className="text-xl font-semibold text-text-primary mb-6 flex items-center">
          <Shield className="w-5 h-5 mr-2" />
          Risk Mitigation & Compliance Benefits
        </h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {riskMitigationBenefits.map((risk, index) => (
            <div key={index} className="bg-surface-light rounded-lg p-4 border border-border-color">
              <div className="flex items-start justify-between mb-3">
                <h3 className="font-semibold text-text-primary">{risk.risk}</h3>
                <div className="flex space-x-2">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    risk.impact === 'High' ? 'bg-red-100 text-red-700' :
                    risk.impact === 'Medium' ? 'bg-yellow-100 text-yellow-700' :
                    'bg-green-100 text-green-700'
                  }`}>
                    {risk.impact} Impact
                  </span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    risk.probability === 'High' ? 'bg-red-100 text-red-700' :
                    risk.probability === 'Medium' ? 'bg-yellow-100 text-yellow-700' :
                    'bg-green-100 text-green-700'
                  }`}>
                    {risk.probability} Probability
                  </span>
                </div>
              </div>
              <p className="text-sm text-text-secondary mb-3">{risk.mitigation}</p>
              <div className="flex items-center justify-between">
                <span className="text-sm text-text-secondary">Risk Reduction:</span>
                <span className="text-lg font-bold text-secondary">{risk.reduction}</span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Industry Comparison */}
      <div className="bg-surface border border-border-color rounded-xl p-4 sm:p-6">
        <h2 className="text-xl font-semibold text-text-primary mb-6 flex items-center">
          <BarChart3 className="w-5 h-5 mr-2" />
          Industry Performance Comparison
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="mb-4">
              <div className="text-sm font-medium text-text-primary mb-2">Liquidation Rate</div>
              <div className="flex items-center justify-center space-x-4">
                <div className="text-center">
                  <div className="text-lg font-bold text-red-500">{financialProjections.industryAverages.liquidationRate}%</div>
                  <div className="text-xs text-text-muted">Industry Avg</div>
                </div>
                <ArrowUpRight className="w-4 h-4 text-secondary rotate-180" />
                <div className="text-center">
                  <div className="text-lg font-bold text-secondary">{financialProjections.withZScore.liquidationRate}%</div>
                  <div className="text-xs text-text-muted">With zScore</div>
                </div>
              </div>
            </div>
            <div className="text-sm font-medium text-secondary">
              {financialProjections.improvements.liquidationReduction.toFixed(1)}% improvement
            </div>
          </div>

          <div className="text-center">
            <div className="mb-4">
              <div className="text-sm font-medium text-text-primary mb-2">Bad Debt Ratio</div>
              <div className="flex items-center justify-center space-x-4">
                <div className="text-center">
                  <div className="text-lg font-bold text-red-500">{financialProjections.industryAverages.badDebtRatio}%</div>
                  <div className="text-xs text-text-muted">Industry Avg</div>
                </div>
                <ArrowUpRight className="w-4 h-4 text-secondary rotate-180" />
                <div className="text-center">
                  <div className="text-lg font-bold text-secondary">{financialProjections.withZScore.badDebtRatio}%</div>
                  <div className="text-xs text-text-muted">With zScore</div>
                </div>
              </div>
            </div>
            <div className="text-sm font-medium text-secondary">
              {financialProjections.improvements.badDebtReduction.toFixed(1)}% improvement
            </div>
          </div>

          <div className="text-center">
            <div className="mb-4">
              <div className="text-sm font-medium text-text-primary mb-2">User Churn Rate</div>
              <div className="flex items-center justify-center space-x-4">
                <div className="text-center">
                  <div className="text-lg font-bold text-red-500">{financialProjections.industryAverages.userChurnRate}%</div>
                  <div className="text-xs text-text-muted">Industry Avg</div>
                </div>
                <ArrowUpRight className="w-4 h-4 text-secondary rotate-180" />
                <div className="text-center">
                  <div className="text-lg font-bold text-secondary">{financialProjections.withZScore.userChurnRate}%</div>
                  <div className="text-xs text-text-muted">With zScore</div>
                </div>
              </div>
            </div>
            <div className="text-sm font-medium text-secondary">
              {financialProjections.improvements.userRetentionIncrease.toFixed(1)}% improvement
            </div>
          </div>

          <div className="text-center">
            <div className="mb-4">
              <div className="text-sm font-medium text-text-primary mb-2">Operational Costs</div>
              <div className="flex items-center justify-center space-x-4">
                <div className="text-center">
                  <div className="text-lg font-bold text-red-500">{financialProjections.industryAverages.operationalCosts}%</div>
                  <div className="text-xs text-text-muted">Industry Avg</div>
                </div>
                <ArrowUpRight className="w-4 h-4 text-secondary rotate-180" />
                <div className="text-center">
                  <div className="text-lg font-bold text-secondary">{financialProjections.withZScore.operationalCosts}%</div>
                  <div className="text-xs text-text-muted">With zScore</div>
                </div>
              </div>
            </div>
            <div className="text-sm font-medium text-secondary">
              {financialProjections.improvements.costReduction.toFixed(1)}% improvement
            </div>
          </div>
        </div>
      </div>

      {/* Call to Action */}
      <div className="bg-gradient-to-r from-primary to-secondary rounded-xl p-6 text-white">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">Ready to Transform Your Lending Protocol?</h2>
          <p className="text-white/90 mb-6 max-w-2xl mx-auto">
            Based on 17 months of proven results: 37% liquidation reduction, 36% bad debt reduction,
            and $4.5M annual profit lift. Join the protocols already benefiting from zScore.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="px-6 py-3 bg-white text-primary font-semibold rounded-lg hover:bg-white/90 transition-colors">
              Schedule Demo
            </button>
            <button className="px-6 py-3 border border-white/30 text-white font-semibold rounded-lg hover:bg-white/10 transition-colors">
              Download Full Report
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
