'use client'

import { useState } from 'react'
import { Brain, Zap, Target, TrendingUp, AlertCircle, CheckCircle, ChevronRight, ChevronDown } from 'lucide-react'
import { cn } from '@/lib/utils'

export default function LLMSidebar() {
  const [isExpanded, setIsExpanded] = useState(true)

  const aiInsights = [
    {
      title: "Risk Pattern Detection",
      confidence: 94,
      insight: "AI identified 3 new risk patterns in wallet behavior that correlate with liquidation events within 24-48 hours.",
      impact: "High",
      status: "active"
    },
    {
      title: "Predictive Modeling",
      confidence: 87,
      insight: "Machine learning models predict liquidation probability with 87% accuracy using zScore and on-chain behavior.",
      impact: "Critical",
      status: "active"
    },
    {
      title: "Anomaly Detection",
      confidence: 91,
      insight: "Detected unusual trading patterns in 156 wallets that may indicate coordinated liquidation attacks.",
      impact: "Medium",
      status: "monitoring"
    }
  ]

  const quickMetrics = [
    { label: "Models Active", value: "12", icon: Brain },
    { label: "Daily Predictions", value: "2.4M", icon: Target },
    { label: "Avg Accuracy", value: "89.2%", icon: CheckCircle },
    { label: "Response Time", value: "<100ms", icon: Zap }
  ]

  return (
    <div className="bg-surface border border-border-color rounded-xl overflow-hidden">
      {/* Header - Always Visible */}
      <div
        className="p-4 cursor-pointer hover:bg-surface-light transition-colors border-b border-border-color"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-br from-accent/20 to-primary/20 rounded-lg flex items-center justify-center">
              <Brain className="w-4 h-4 text-accent" />
            </div>
            <div>
              <div className="flex items-center space-x-2">
                <h3 className="font-semibold text-text-primary text-sm">AI Insights</h3>
                <span className="px-2 py-0.5 bg-accent/10 text-accent text-xs rounded-full border border-accent/20">
                  Live
                </span>
              </div>
              <p className="text-xs text-text-secondary">ML-powered analytics</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <div className="text-right">
              <p className="text-lg font-bold text-accent">89.2%</p>
              <p className="text-xs text-text-secondary">Accuracy</p>
            </div>
            {isExpanded ? (
              <ChevronDown className="w-4 h-4 text-text-secondary" />
            ) : (
              <ChevronRight className="w-4 h-4 text-text-secondary" />
            )}
          </div>
        </div>
      </div>

      {/* Expandable Content */}
      {isExpanded && (
        <div className="border-t border-border-color">
          {/* Quick Metrics */}
          <div className="p-4 space-y-3">
            <h4 className="text-sm font-medium text-text-primary mb-3">Quick Metrics</h4>
            <div className="grid grid-cols-2 gap-3">
              {quickMetrics.map((metric, index) => {
                const Icon = metric.icon
                return (
                  <div key={index} className="bg-surface-light rounded-lg p-3">
                    <div className="flex items-center space-x-2 mb-1">
                      <Icon className="w-3 h-3 text-primary" />
                      <span className="text-xs text-text-secondary">{metric.label}</span>
                    </div>
                    <p className="text-sm font-semibold text-text-primary">{metric.value}</p>
                  </div>
                )
              })}
            </div>
          </div>

          {/* AI Insights */}
          <div className="p-4 border-t border-border-color">
            <h4 className="text-sm font-medium text-text-primary mb-3">Real-time Insights</h4>
            <div className="space-y-3">
              {aiInsights.map((insight, index) => (
                <div key={index} className="bg-surface-light rounded-lg p-3">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <div className={`w-2 h-2 rounded-full ${
                        insight.status === 'active' ? 'bg-secondary' : 'bg-accent'
                      }`} />
                      <h5 className="text-xs font-medium text-text-primary">{insight.title}</h5>
                    </div>
                    <span className={`text-xs px-2 py-0.5 rounded border ${
                      insight.impact === 'Critical' ? 'bg-danger/10 text-danger border-danger/20' :
                      insight.impact === 'High' ? 'bg-accent/10 text-accent border-accent/20' :
                      'bg-primary/10 text-primary border-primary/20'
                    }`}>
                      {insight.impact}
                    </span>
                  </div>
                  <p className="text-xs text-text-secondary leading-relaxed mb-2">{insight.insight}</p>
                  <div className="flex items-center justify-between">
                    <div className="flex-1 bg-surface rounded-full h-1.5 mr-2">
                      <div
                        className="bg-gradient-to-r from-primary to-primary-light h-1.5 rounded-full"
                        style={{ width: `${insight.confidence}%` }}
                      />
                    </div>
                    <span className="text-xs text-text-secondary">{insight.confidence}%</span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Quick Recommendations */}
          <div className="p-4 border-t border-border-color">
            <h4 className="text-sm font-medium text-text-primary mb-3">AI Recommendations</h4>
            <div className="space-y-2">
              <div className="bg-surface-light rounded-lg p-3">
                <div className="flex items-start space-x-2">
                  <TrendingUp className="w-3 h-3 text-secondary mt-0.5" />
                  <div>
                    <p className="text-xs font-medium text-text-primary">Optimize Collateral</p>
                    <p className="text-xs text-text-secondary">Increase ratios for zScore &lt; 150</p>
                  </div>
                </div>
              </div>
              <div className="bg-surface-light rounded-lg p-3">
                <div className="flex items-start space-x-2">
                  <AlertCircle className="w-3 h-3 text-accent mt-0.5" />
                  <div>
                    <p className="text-xs font-medium text-text-primary">Enhanced Monitoring</p>
                    <p className="text-xs text-text-secondary">234 wallets need attention</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
