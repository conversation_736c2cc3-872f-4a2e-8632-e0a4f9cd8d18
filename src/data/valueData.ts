// ROI and Value proposition data for zScore implementation

export interface ROIMetrics {
  liquidationReduction: number
  badDebtReduction: number
  revenueIncrease: number
  costSavings: number
  userRetention: number
  operationalEfficiency: number
}

export interface ProtocolSize {
  tvl: string
  users: string
  multiplier: number
}

export interface BusinessImpact {
  title: string
  value: string
  description: string
  icon: string
  color: string
  bgColor: string
  trend: string
}

export interface ImplementationPhase {
  phase: string
  duration: string
  investment: string
  roi: string
  status: 'immediate' | 'short-term' | 'long-term'
  benefits: string[]
}

export interface CompetitiveAdvantage {
  feature: string
  traditional: string
  withZScore: string
  improvement: string
}

export interface CaseStudy {
  name: string
  tvl: string
  users: string
  liquidationReduction: number
  badDebtReduction: number
  revenueIncrease: number
  implementationTime: string
  testimonial: string
  author: string
  icon: string
}

// ROI metrics by timeframe - EXACT DATA from Zeru Finance zScore ROI Impact Report (Jan 2024 – May 2025)
export const roiMetrics: Record<string, ROIMetrics> = {
  '17-months-actual': {
    liquidationReduction: 37, // PROVEN: Default/Liquidation Rate 10.2% → 6.4% (37% reduction, p<0.001)
    badDebtReduction: 36, // PROVEN: Bad Debt 0.50% → 0.32% (36% reduction, p<0.001)
    revenueIncrease: 12, // PROVEN: Interest Revenue $7,500 → $8,400 (+$900/user, 12%, p=0.03)
    costSavings: 4.5, // PROVEN: $4.5M annual profit lift ($98M → $102.5M)
    userRetention: 15, // PROVEN: Repeat Borrowing 40% → 46% (15% relative improvement, p=0.008)
    operationalEfficiency: 8 // PROVEN: Loan Volume Growth +22% → +30% (8% diff-in-diff, p=0.02)
  },
  '12-months-projected': {
    liquidationReduction: 37, // Based on 17-month proven results
    badDebtReduction: 36, // Based on 17-month proven results
    revenueIncrease: 12, // Based on 17-month proven results
    costSavings: 4.5, // Based on 17-month proven results
    userRetention: 15, // Based on 17-month proven results
    operationalEfficiency: 8 // Based on 17-month proven results
  }
}

// Protocol size configurations
export const protocolSizes: Record<string, ProtocolSize> = {
  small: { tvl: '50M', users: '5K', multiplier: 0.5 },
  medium: { tvl: '500M', users: '25K', multiplier: 1.0 },
  large: { tvl: '2B', users: '100K', multiplier: 2.5 }
}

// Implementation phases with detailed ROI projections
export const implementationPhases: ImplementationPhase[] = [
  {
    phase: "Phase 1: Integration",
    duration: "2-3 months",
    investment: "$150K",
    roi: "Break-even in 4 months",
    status: "immediate",
    benefits: [
      "Basic zScore integration",
      "Risk categorization",
      "Initial liquidation reduction",
      "Automated risk scoring"
    ]
  },
  {
    phase: "Phase 2: Optimization",
    duration: "3-4 months",
    investment: "$200K",
    roi: "300% ROI by month 8",
    status: "short-term",
    benefits: [
      "Advanced risk models",
      "Dynamic pricing",
      "Cross-chain reputation",
      "Real-time monitoring"
    ]
  },
  {
    phase: "Phase 3: Advanced Features",
    duration: "2-3 months",
    investment: "$100K",
    roi: "500% ROI by month 12",
    status: "long-term",
    benefits: [
      "AI-powered predictions",
      "Automated recommendations",
      "Governance integration",
      "Advanced analytics"
    ]
  }
]

// Competitive advantages analysis - ACTUAL DATA from ROI Impact Report
export const competitiveAdvantages: CompetitiveAdvantage[] = [
  {
    feature: "Default/Liquidation Rate",
    traditional: "10.2% baseline liquidation rate",
    withZScore: "6.4% with zScore integration",
    improvement: "37% reduction in liquidations"
  },
  {
    feature: "Bad Debt Management",
    traditional: "0.50% bad debt ratio",
    withZScore: "0.32% bad debt ratio",
    improvement: "36% reduction in bad debt"
  },
  {
    feature: "Revenue per User",
    traditional: "$7,500 average interest revenue",
    withZScore: "$8,400 average interest revenue",
    improvement: "12% increase (+$900 per user)"
  },
  {
    feature: "Loan-to-Value Optimization",
    traditional: "67% average LTV ratio",
    withZScore: "80% average LTV ratio",
    improvement: "13 percentage points increase"
  },
  {
    feature: "User Retention",
    traditional: "40% repeat borrowing rate",
    withZScore: "46% repeat borrowing rate",
    improvement: "15% relative improvement"
  },
  {
    feature: "Sybil Attack Prevention",
    traditional: "~6% sybil addresses in airdrops",
    withZScore: "<0.2% sybil addresses",
    improvement: "98% reduction in sybil attacks"
  },
  {
    feature: "Wash Trading Detection",
    traditional: "11.2% wash trade volume",
    withZScore: "3.1% wash trade volume",
    improvement: "72% reduction in wash trading"
  },
  {
    feature: "Cross-Chain Coverage",
    traditional: "Single-chain risk assessment",
    withZScore: "Ethereum, Arbitrum, Optimism, Polygon, Base, Avalanche",
    improvement: "Unified multi-chain reputation"
  }
]

// Success stories and case studies - BASED ON ACTUAL ROI REPORT DATA
export const caseStudies: CaseStudy[] = [
  {
    name: "Ethereum L1 Lending Protocols",
    tvl: "$1.5B", // ~$1.5B collateral mentioned in report
    users: "10K+", // ~10,000 borrowers from report
    liquidationReduction: 37, // Actual 37% reduction from report
    badDebtReduction: 36, // Actual 36% reduction from report
    revenueIncrease: 12, // Actual 12% revenue increase from report
    implementationTime: "17 months", // Jan 2024 - May 2025 analysis period
    testimonial: "zScore integration unlocked $200M of additional borrowing capacity while reducing defaults by 37%. The $4.5M annual profit lift exceeded our expectations.",
    author: "Based on ROI Impact Report Data",
    icon: "zap"
  },
  {
    name: "Multi-Chain DeFi Ecosystem",
    tvl: "$2B+", // Ethereum & L2 aggregate
    users: "25K+", // Estimated across all chains
    liquidationReduction: 37, // Consistent across chains per report
    badDebtReduction: 36, // Consistent across chains per report
    revenueIncrease: 12, // Consistent revenue improvement
    implementationTime: "17 months", // Analysis period
    testimonial: "Cross-chain zScore implementation on Ethereum, Arbitrum, Optimism, Polygon, Base, and Avalanche showed consistent 37% liquidation reduction across all networks.",
    author: "Multi-Chain Analysis Results",
    icon: "shield"
  },
  {
    name: "DEX Liquidity Mining Programs",
    tvl: "$1.1B", // $1.00B → $1.10B trading volume increase
    users: "15K+", // Estimated LP and trader base
    liquidationReduction: 72, // 72% reduction in wash trading
    badDebtReduction: 90, // 90% reduction in sybil LP addresses
    revenueIncrease: 30, // 30% increase in effective liquidity
    implementationTime: "17 months", // Analysis period
    testimonial: "zScore filtering eliminated 90% of sybil addresses and reduced wash trading by 72%, while increasing effective liquidity by 30% and improving LP retention by 22%.",
    author: "DEX Liquidity Mining Results",
    icon: "target"
  }
]

// Key financial metrics and projections - ACTUAL DATA from ROI Impact Report
export const financialProjections = {
  industryAverages: {
    liquidationRate: 10.2, // Baseline from report
    badDebtRatio: 0.50, // Baseline Bad Debt as % of Loans
    userChurnRate: 60, // Inverse of 40% repeat borrowing rate
    operationalCosts: 15 // Estimated baseline
  },
  withZScore: {
    liquidationRate: 6.4, // With zScore from report
    badDebtRatio: 0.32, // With zScore from report
    userChurnRate: 54, // Inverse of 46% repeat borrowing rate
    operationalCosts: 10 // Estimated improvement
  },
  improvements: {
    liquidationReduction: 37.0, // Actual 37% reduction from report
    badDebtReduction: 36.0, // Actual 36% reduction from report
    userRetentionIncrease: 15.0, // Actual 15% relative improvement from report
    costReduction: 33.3 // Estimated based on operational improvements
  }
}

// Market opportunity and addressable market
export const marketOpportunity = {
  totalAddressableMarket: "$18.7B",
  servicableAddressableMarket: "$8.2B",
  servicableObtainableMarket: "$1.4B",
  marketGrowthRate: 23.5,
  protocolsAddressed: 150,
  potentialRevenue: "$420M"
}

// Risk mitigation benefits
export const riskMitigationBenefits = [
  {
    risk: "Liquidation Cascade Events",
    impact: "High",
    probability: "Medium",
    mitigation: "Real-time risk monitoring and early warning systems",
    reduction: "65%"
  },
  {
    risk: "Bad Debt Accumulation",
    impact: "High",
    probability: "High",
    mitigation: "Predictive modeling and proactive interventions",
    reduction: "72%"
  },
  {
    risk: "Regulatory Compliance",
    impact: "Medium",
    probability: "Low",
    mitigation: "Transparent and auditable risk assessment",
    reduction: "85%"
  },
  {
    risk: "User Trust Issues",
    impact: "Medium",
    probability: "Medium",
    mitigation: "Transparent reputation scoring and explanations",
    reduction: "45%"
  }
]

// Technology advantages
export const technologyAdvantages = [
  {
    feature: "EigenLayer Integration",
    benefit: "Decentralized and secure reputation system",
    impact: "Enhanced trust and security"
  },
  {
    feature: "Multi-chain Support",
    benefit: "Unified reputation across all major chains",
    impact: "Comprehensive risk assessment"
  },
  {
    feature: "Real-time Processing",
    benefit: "Instant risk score updates",
    impact: "Immediate risk mitigation"
  },
  {
    feature: "AI-Powered Analytics",
    benefit: "Advanced pattern recognition",
    impact: "Predictive risk management"
  },
  {
    feature: "Open Source",
    benefit: "Transparent and auditable algorithms",
    impact: "Community trust and adoption"
  }
]
